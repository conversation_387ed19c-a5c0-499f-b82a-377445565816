import { query } from "../db";
import {
    handleResponse,
    reducePercent,
} from "../common";
import {
    createOrderDb,
    getCartItemsByArtistId,
    getOrdersQuery,
    updateCartItem,
    updateOrderStatus,
    updateOrderStatusDB,
    updatePaymentFailed,
    updatePaymentStatus,
    upsertUserCart,
} from "../services/merch";
import { OrderBody, UserAddress } from "../types/db_types";
import {
    CURRENCIES,
    DEFAULT_PAGE_SIZE,
    ORDER_STATUS,
    ORDER_STATUS_CSV,
    PAYMENT_METHOD,
    PAYPAL_PLATFORM_FEES,
    PHONE_PE_STATUS,
    REFUND_STATUS,
} from "../common/constants";
import { decodeBase64, runPayAPI } from "../services/pg";
import { v4 as uuidv4 } from 'uuid';
import { addAddressQuery, coursesInCartOrderQuery, deleteCartItemsByArtistIdQuery, getDiscountPercentageQuery, getProductItemByIdQuery, getUserAddressByIdQuery, insertPgLogsQuery, insertUserCourseQuery, offerUsedCountQuery, orderDetailForCancelQuery, orderInvoiceQuery, productItemQuery, transactionBegin, transactionCommit, transactionRollback, userAddressinvoiceQuery } from "../common/db_constants";
import { PaymentCallback, PayInitBody } from "../types/pg_types";
import { generatePDF, getProductArr } from "../services/pdf";
import logger from "../logger";
var XLSX = require("xlsx");
import fs from "fs";
import { OrderExcelItems, PayGLocalTxnData, PaymentCallbackDefault } from "../types";
import { getFileSize, removeFile } from "../services/file";
import { OrderDATA, mailCancelledText, mailShippedText } from "../admin/mail_accept_text";
import { sendEmail } from "../services/mailer";
import {
    getOfferData,
    getOrderDetail,
    getPurchaseAddress,
    getPurchasedProducts,
    validateOfferData,
} from "../services/orders";
import { createOrder } from "../services/paypal";
import { generateSignedUrl, uploadFile ,getSignedPlaylistWithSegments} from "../services/aws";
import { initPayment } from "../services/payglocal";

// const paymentMethodMap: Record<string, number> = {
//   phonepe: 1,
//   paypal: 2,
//   payglocal: 3
// };

const getProductsQuery = `select p.id,p.name,p.images,'PRODUCT' order_type,
array_agg(
	JSONB_BUILD_OBJECT(
		'currency',pcr.name,
		'currency_id',pcr.id,
		'price' , pp.price,
		'max_price' , pp.max_price,
		'gst' , pp.gst)
			) currencies from products p
inner join product_prices pp on pp.product_id = p.id 
inner join payment_currency pcr on pcr.id = pp.currency
inner join product_category pc on pc.id = p.product_category_id
where p.is_active = true and p.artist_id = $1 or $1 is null
group by p.id
union all
select cd.id,cd.title as name,array[''] images,'COURSE' order_type,
array_agg(
	JSONB_BUILD_OBJECT(
		'currency',pcr.name,
		'currency_id',pcr.id,
		'price' , pp.price,
		'max_price' , pp.max_price,
		'gst' , pp.gst)
			) currencies from course_details cd
inner join product_prices pp on pp.course_id = cd.id 
inner join payment_currency pcr on pcr.id = pp.currency
where cd.is_active = true and cd.artist_id = $1 or $1 is null
group by cd.id
union all
select am.id,amd.name,array[''] images,'MEET' order_type, 
array_agg(
	JSONB_BUILD_OBJECT(
		'currency',pcr.name,
		'currency_id',pcr.id,
		'price' , pp.price,
		'max_price' , pp.max_price,
		'gst' , pp.gst)
			) currencies from artist_meets am
inner join artist_meet_detail amd on amd.artist_meet_id = am.id
inner join product_prices pp on pp.meet_id = am.id 
inner join payment_currency pcr on pcr.id = pp.currency
where am.is_active = true and am.artist_id = $1 or $1 is null
group by am.id,amd.name
union all
select pv.id,pv.title as name,array[''] images,'PAID_VIDEO' order_type,
array_agg(
	JSONB_BUILD_OBJECT(
		'currency',pcr.name,
		'currency_id',pcr.id,
		'price' , pp.price,
		'max_price' , pp.max_price,
		'gst' , pp.gst)
			) currencies from paid_videos pv
inner join product_prices pp on pp.video_id = pv.id 
inner join payment_currency pcr on pcr.id = pp.currency
where pv.is_active = true
group by pv.id;`;

const getPdtsQuery = `select 
p.id,p.name title,p.description,p.price,p.tax,
a.name brand_name,
p.is_external, p.is_internal, p.external_link,
p.design_data,
array_agg(
	JSONB_BUILD_OBJECT(
		'currency',pcr.name,
		'currency_id',pcr.id,
		'price' , pp.price,
		'max_price' , pp.max_price,
		'gst' , pp.gst)
			) currencies,

(
    SELECT json_agg(
        json_build_object(
            'id', pc.id,
            'country_name', cl.name,
            'country_id', cl.id,
            'item_id', pc.product_id
        )
    )
    FROM product_country pc
    INNER JOIN country_list cl ON cl.id = pc.country_id
    WHERE pc.product_id = p.id
    GROUP BY pc.product_id
) AS countries,
p.metadata,p.is_out_of_stock,
p.size_chart,
p.images,COALESCE((select array_agg(distinct short_name) from variation where product_category_id = p.product_category_id),ARRAY[]::TEXT[]) varaiant_option,
dispaly_images
from products p
inner join product_prices pp on pp.product_id = p.id 
inner join payment_currency pcr on pcr.id = pp.currency
inner join artist a on a.id = p.artist_id
where p.id = $1 
group by p.id,a.name`;

const getVariantsByPdtIdQuery = `select pi.id,p.id product_id,pi.image_url "imageURL",
case when pi.is_limited_quantity = true and available_quantity = 0 then false else true end "isStock",
array_agg(JSONB_BUILD_OBJECT(coalesce(v.short_name,''),coalesce(vo.value,''))),
pi.is_out_of_stock,
pi.total_quantity,
pi.available_quantity
from products p
inner join product_item pi on pi.product_id = p.id
left join product_configuration pc on pc.product_item_id = pi.id
left join variation_option vo on vo.id = pc.variation_option_id
left join variation v on v.id = vo.variation_id
where p.id = $1 and pi.is_active = true
group by pi.id,p.id`;

const userOrdersV2Query = `(select od.id order_id,oi.id order_item_id,od.created ordered_on,
    pi.title "name",pi.image_url images,os.name status,'PRODUCT' order_type
    from order_items oi
    inner join product_item pi on pi.id = oi.product_item_id
    inner join order_details od on od.id = oi.order_id
    inner join order_status_history osh on osh.order_item_id = oi.id 
    inner join order_status os on os.id = osh.status_id
    inner join products p 
      ON (
        (pi.product_type != 2 AND p.id = pi.product_id)
        OR
        (pi.product_type = 2 AND p.id = pi.course_product_id)
      )
    where od.user_id = $1)
    union all
    (select 
    cpl.id order_id,cpl.id order_item_id,cpl.created ordered_on,
    cd.title "name", null images,ps.name status,'COURSE' order_type
    from course_purchase_logs cpl
    inner join payment_status ps on ps.id = cpl.payment_status
    inner join course_details cd on cd.id = cpl.course_id
    where cpl.user_id = $1)
    union all
    (select aml.id order_id,aml.id order_item_id,aml.created ordered_on,
    amd.name, null images,ps.name status,'MEET' order_type
    from artist_meeting_logs aml
    inner join payment_status ps on ps.id = aml.payment_status
    inner join artist_meet_detail amd on amd.artist_meet_id = aml.artist_meet_id
    where aml.user_id = $1)
    union all
    (select upv.id order_id,upv.id order_item_id,upv.created ordered_on,
    pv.title "name", null images,ps.name status,'PAID_VIDEO' order_type
    from user_paid_video_entries upv
    inner join payment_transactions pt on pt.id = upv.txn_id
    inner join payment_status ps on ps.id = pt.payment_status
    inner join paid_videos pv on pv.id = upv.video_id
    where upv.user_id = $1
    )
    order by 3 desc
    LIMIT $2
    OFFSET (($3 - 1) * $2);`;

const getTotalOrderItemsQuery = `select (count(distinct oi.id) + count(distinct cpl.id) +
count(distinct upv.id) + count(distinct aml.id)) as total_items from users u
left join order_details od on od.user_id = u.id
left join order_items oi on oi.order_id = od.id
left join course_purchase_logs cpl on cpl.user_id = u.id
left join user_paid_video_entries upv on upv.user_id = u.id
left join artist_meeting_logs aml on aml.user_id = u.id
where u.id = $1`;

const deleteOrderQuery = `delete from shopping_cart_items where cart_id in (select id from shopping_cart where user_id = $1);`;

const getAddressQuery = `select 
coalesce(ua.email,u.email) email,
coalesce(ua.phone,u.mobile_number) phone,
u.id user_id,
ua.id address_id, 
ua.first_name,
ua.last_name,
ua.address_line_one,
ua.address_line_two,
ua.address_line_three,
ua.city,
ua.state,
ua.pincode,
ua.country
from users u
left join user_address ua on ua.user_id = u.id
where u.id =$1 AND ua.in_use = true`;

const getAddressQueryDfltAddNotNull = `
SELECT 
    COALESCE(ua.email, u.email) AS email,
    COALESCE(ua.phone, u.mobile_number) AS phone,
    ua.id AS address_id,
    u.id AS user_id,
    ua.first_name,
    ua.last_name,
    ua.address_line_one,
    ua.address_line_two,
    ua.address_line_three,
    ua.city,
    ua.state,
    ua.pincode,
    ua.country
FROM users u
LEFT JOIN user_address ua ON ua.user_id = u.id
LEFT JOIN user_profile up ON up.user_id = u.id
WHERE u.id = $1 
  AND ua.in_use = true
  AND (
      up.default_address IS NOT NULL 
      OR ua.id = (
          SELECT MAX(id) 
          FROM user_address 
          WHERE user_id = $1
      )
  );
`;

const getAddresswithidQuery = `select 
ua.email email,
ua.phone phone,
ua.id address_id,
ua.first_name,
ua.last_name,
ua.address_line_one,
ua.address_line_two,
ua.address_line_three,
ua.city,
ua.state,
ua.pincode,
ua.country
from users u
left join user_address ua on ua.user_id = u.id
where u.id = $1 AND ua.in_use = true;`;

const getuserProfilequery = `
select 
ua.id,
ua.default_address,
ua.image,
ua.name, 
coalesce(ua.email,u.email) email,
coalesce(ua.phone,u.mobile_number) phone
from users u
left join user_profile ua on ua.user_id = u.id
where u.id = $1`;

export const insertuserProfilequery = `INSERT INTO user_profile (name,email,image,phone,user_id, default_address)
VALUES ($1,$2,$3,$4,$5,$6);`;

const insertuserProfileImgquery = `INSERT INTO user_profile (image,user_id)
VALUES ($1,$2);`;

const updateuserProfilequery = `UPDATE user_profile up
SET name = $1,email=$2 ,phone = $3, default_address = $5
WHERE  up.user_id = $4;`;

const updateuserProfileImgquery = `UPDATE user_profile up
SET image = $1
WHERE  up.user_id = $2;`;

export const updateAddressQuery = `
UPDATE user_address
SET 
    first_name = $2,
    last_name = $3,
    address_line_one = $4,
    address_line_two = $5,
    city = $6,
    state = $7,
    pincode = $8,
    phone = $9,
    email = $10,
    country = $11,
    address_line_three = $12
WHERE user_id = $1 and id = $13;`;

export const deleteAddressQuery = `DELETE FROM user_address WHERE id = $1;` 

export const markAddressNotInUseQuery = `UPDATE user_address SET in_use = false WHERE id = $1;`;

const getPopupQuery = `select title,metadata ->> 'image' images,description "text",metadata ->> 'audio' "audioLink",metadata ->> 'video' "video" from artist_popup where popup_id = $1;`;

const getVidQuery = `select video_id,video from artist_video where video_id = $1 and is_active = true;`;

const sizeVariantsQuery = `select v.short_name variation_name,array_agg(vo.short_value order by vo.order_number) "key" from product_item pi
inner join product_configuration pc on pc.product_item_id = pi.id
inner join variation_option vo on vo.id = pc.variation_option_id
inner join variation v on v.id = vo.variation_id
where pi.product_id = $1
group by v.short_name`;

const getOrderHistoryQuery = `
(
	select 
		od.id order_id,
		od.total_items order_item_count,
		'PRODUCT' order_type,
		od.created ordered_on,
		pi.image_url images,
		os.name status
	from order_details od
	inner join order_items oi on oi.id = (
		select 
			io.id 
		from order_items io
		where io.order_id = od.id
		limit 1
	)
	inner join product_item pi on pi.id = oi.product_item_id
	inner join order_status_history osh on osh.order_item_id = oi.id
	inner join order_status os on os.id = osh.status_id
	where od.user_id = $1
	order by od.id desc
)
union all
(
	SELECT DISTINCT ON (cpl.id)
    cpl.id AS order_id,
    1 AS order_item_count,
    'COURSE' AS order_type,
    cpl.created AS ordered_on,
    cd.image AS images,
    ps.name AS status
FROM course_purchase_logs cpl
INNER JOIN payment_status ps ON ps.id = cpl.payment_status
INNER JOIN course_details cd 
  ON (
    (cpl.is_combo_purchase = false AND cd.course_level_id = cpl.course_id)
    OR
    (cpl.is_combo_purchase = true AND cd.id = cpl.course_id)
  )
	where cpl.user_id = $1
	order by cpl.id desc
)
union all
(
	select 
		aml.id order_id,
		1 order_item_count,
		'MEET' order_type,
		aml.created ordered_on,
    	amd.image images,
		ps.name status
    from artist_meeting_logs aml
    inner join payment_status ps on ps.id = aml.payment_status
    inner join artist_meet_detail amd on amd.artist_meet_id = aml.artist_meet_id
    where aml.user_id = $1
)
union all
(
	select 
		upv.id order_id,
		1 order_item_count,
		'PAID_VIDEO' order_type,
		upv.created ordered_on,
		pv.image images,
		ps.name status
    from user_paid_video_entries upv
    inner join payment_transactions pt on pt.id = upv.txn_id
    inner join payment_status ps on ps.id = pt.payment_status
    inner join paid_videos pv on pv.id = upv.video_id
    where upv.user_id = $1
)
order by 4 desc
LIMIT $2
OFFSET (($3 - 1) * $2);

`;

const getDiscountQuery = `select discount,code from coupon_codes where id = $1;`
export const getProducts = async (req: any, res: any) => {
    let response: any = {};
    try {
        const { artistId } = req.params;

        const products = await query(getProductsQuery, [Number(artistId) || null]);

        response = {
            status: true,
            message: `Success`,
            data: {
                products: products.rows,
            },
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error,
        };
        return handleResponse(res, response, 500);
    }
};

export const getProductDetails = async (req: any, res: any) => {
    let response: any = {};
    try {
        const { productId } = req.params;

        if (!productId) {
            response = {
                status: false,
                message: `Product Id Not Available`,
            };
            return handleResponse(res, response, 400);
        }

        const products = await query(getPdtsQuery, [Number(productId)]);

        const pdtVariats = await query(getVariantsByPdtIdQuery, [
            Number(productId),
        ]);

        let variantOptions: any[] = [];

        const variantsOrderrows = await query(sizeVariantsQuery, [
            Number(productId),
        ]);

        const variants = pdtVariats.rows.map((el) => {
            const eData = el.array_agg.reduce(
                (result: { [x: string]: any }, item: { [x: string]: any }) => {
                    const key = Object.keys(item)[0];
                    if (!variantOptions.includes(key)) variantOptions.push(key);
                    result[key] = item[key];
                    return result;
                },
                {}
            );

            const { array_agg, ...data } = el;

            return {
                ...data,
                ...eData,
            };
        });

        const images = new Set(variants.map((item) => item.imageURL));

        let unqImages = Array.from(images);

        unqImages = unqImages.map((el, i) => {
            return {
                id: i + 1,
                imageURL: el,
                thumbnailURL: el,
            };
        });

        let detail = products.rows[0];

        detail.varaiant_option = variantOptions;

        detail.varaiant_option = detail.varaiant_option.filter((element: null) => {
            return element !== null && element !== "";
        });

        detail.varaiant_option.sort().reverse();

        detail.variation_order = variantsOrderrows.rows
            .map((el) => {
                let obj: any = {};
                obj[el.variation_name] = [...new Set(el.key)];
                return obj;
            })
            .sort()
            .reverse();

        let currency = detail.currencies.find(
            (el: any) => el.currency_id === CURRENCIES.INR
        );
        detail.currency_symbol = "₹";
        detail.price = currency.price;
        detail.tax = currency.gst;
        detail.currency = currency.currency;

        response = {
            status: true,
            message: `Success`,
            data: {
                imageList: unqImages,
                details: {
                    ...detail,
                    variants: variants,
                },
            },
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error,
        };
        return handleResponse(res, response, 500);
    }
};

const cancelOrderDetailQuery = `UPDATE order_details
    SET order_status = $1, refund_status = $2
    WHERE id = $3;`;

const cancelOrderItemsQuery = `UPDATE order_status_history
    SET status_id = $1
    FROM order_items oi
    INNER JOIN order_details od ON od.id = oi.order_id
    WHERE od.id = $2 AND order_status_history.order_item_id = oi.id;`;

export const postOrderStatusCancel = async (req: any, res: any) => {
    let response: any = {};
    const { order_id, order_type } = req.body;

    const user = req.user;

    if (!order_id || !order_type) {
        response = {
            status: false,
            message: `Missing Params`,
        };
        return handleResponse(res, response, 400);
    }

    try {
        const orderDetail = await getOrderDetail(order_id, user.id, order_type);

        if (
            orderDetail.order_status === "PLACED" &&
            orderDetail.is_cancellable === true &&
            orderDetail.payment_status === "PAYMENT_SUCCESS"
        ) {
            await query(transactionBegin, []);

            const orderDet: any = await query(cancelOrderDetailQuery, [
                ORDER_STATUS.CANCELLED,
                REFUND_STATUS.INIT,
                order_id,
            ]);

            if (!orderDet.rowCount) {
                await query(transactionRollback, []);
                response = {
                    status: false,
                    message: `Invalid product item id`,
                };
                return handleResponse(res, response, 400);
            }

            const orderItem: any = await query(cancelOrderItemsQuery, [
                ORDER_STATUS.CANCELLED,
                order_id,
            ]);
            if (!orderItem.rowCount) {
                await query(transactionRollback, []);
                response = {
                    status: false,
                    message: `Invalid product item id`,
                };
                return handleResponse(res, response, 400);
            }

            const purchaseDetails = await query(orderDetailForCancelQuery, [order_id])

            if (!purchaseDetails.rowCount) {
                await query(transactionRollback, []);
                response = {
                    status: false,
                    message: `Order not found`,
                };
                return handleResponse(res, response, 400);
            }

            let data = {
                customerName: purchaseDetails.rows[0].customerName,
                products: purchaseDetails.rows[0].products
            };
            const html = mailCancelledText(data);
            await sendEmail([purchaseDetails.rows[0].email], "Your Order Has Been Cancelled", html, undefined)

            await query(transactionCommit, []);

            response = {
                status: "success",
                message: `Order Cancelled successfully.`,
            };
            return handleResponse(res, response, 200);
        }

        response = {
            status: false,
            message: `Cancel order failed.`,
        };
        return handleResponse(res, response, 400);
    } catch (error: any) {
        response = {
            status: false,
            message: error.message ? error.message : `Request failed`,
        };
        return handleResponse(res, response, 500);
    }
};

export const getProductPrice = `SELECT pp.price, pp.max_price, pp.gst, pcr.name AS currency, pcr.id AS currency_id
FROM product_prices pp
INNER JOIN payment_currency pcr ON pcr.id = pp.currency
WHERE pp.product_id = $1
  AND pcr.id = $2`;

export const getMeetPrice = `SELECT pp.price, pp.max_price, pp.gst, pcr.name AS currency, pcr.id AS currency_id
FROM product_prices pp
INNER JOIN payment_currency pcr ON pcr.id = pp.currency
WHERE pp.meet_id = $1 
  AND pcr.id = $2`;

export const getCoursePrice = `
SELECT 
  SUM(pp.price) AS price,
  SUM(pp.max_price) AS max_price,
  MIN(pp.gst) AS gst,
  pcr.name AS currency, 
  pcr.id AS currency_id
FROM product_prices pp
INNER JOIN payment_currency pcr ON pcr.id = pp.currency
INNER JOIN course_details cd ON cd.id = pp.course_id
WHERE ($3::boolean = false AND cd.course_level_id = $1)
AND pcr.id = $2
GROUP BY pcr.id, pcr.name
LIMIT 1;
`;;

export const getVideoPrice = `SELECT pp.price, pp.max_price, pp.gst, pcr.name AS currency, pcr.id AS currency_id
FROM product_prices pp
INNER JOIN payment_currency pcr ON pcr.id = pp.currency
WHERE pp.video_id = $1 
  AND pcr.id = $2`;

export const getUserCartSingleCheckout = async (req: any, res: any) => {
    let response: any = {};
    const user = req.user;

    const { coupon, currency, productItemId, type,courselevelavail } = req.query;
    let currency1 = currency || CURRENCIES.INR;

    if (!Object.values(CURRENCIES).includes(Number(currency1))) {
        response = {
            status: false,
            message: `Invalid Currency`,
        };
        return handleResponse(res, response, 400);
    }

    try {
        let cartItems;
        let pdtItem: any;

        if (type === "1") {
            pdtItem = await query(getProductPrice, [productItemId, currency1]);
        } else if (type === "2") {
            pdtItem = await query(getMeetPrice, [productItemId, currency1]);
        } else if (type === "3") {
            pdtItem = await query(getCoursePrice, [productItemId, currency1, courselevelavail]);
        } else if (type === "4") {
            pdtItem = await query(getVideoPrice, [productItemId, currency1]);
        } else {
            response = {
                status: false,
                message: `Type is not correct`,
            };
            return handleResponse(res, response, 400);
        }

        let discountData: any;
        if (coupon) {
            const couponData = await getOfferData(coupon, type == '2' ? productItemId : undefined, type == '3' ? productItemId : undefined, type == '4' ? productItemId : undefined, type == '1' ? productItemId : undefined);
            if (couponData.length) {
                discountData = couponData[0]
            }
        }

        if (discountData) {
            const countData = await query(offerUsedCountQuery, [user.id, discountData.id]);

            if (!discountData.unlimited) {
                if (countData.rowCount) {
                    if (countData.rows[0].total_count >= discountData.total_limit) {
                        response = {
                            status: false,
                            message: `Invalid Coupon`
                        }
                        return handleResponse(res, response, 400);
                    }
                }
            }

            if (discountData.user_limited) {
                if (countData.rows[0].user_count >= discountData.user_limit) {
                    response = {
                        status: false,
                        message: `Invalid Coupon`
                    }
                    return handleResponse(res, response, 400);
                }
            }
        }

        if (discountData && Number(discountData.discount) > 0 && pdtItem.rows.length > 0) {
            cartItems = pdtItem.rows.map(
                (el: { price: any; price_per_qty: number; gst: number }) => {
                    return {
                        ...el,
                        couponValid: true,
                        originalPrice: parseFloat(el.price),
                        price: reducePercent(el.price, el.gst, Number(discountData.discount)),
                        price_per_qty: reducePercent(el.price, el.gst, Number(discountData.discount)),
                    };
                }
            );
        } else {
            cartItems = pdtItem.rows.map(
                (el: { price: any; price_per_qty: number; gst: number }) => {
                    return {
                        ...el,
                        couponValid: false,
                        couponDetail: "Invalid Coupon",
                        price: reducePercent(el.price, el.gst, 0),
                        price_per_qty: reducePercent(el.price, el.gst, 0),
                    };
                }
            );
        }

        response = {
            status: true,
            message: `Discounted value Fetched`,
            data: {
                cartItems: cartItems,
            },
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error,
        };
        return handleResponse(res, response, 500);
    }
};

export const updateCart = async (req: any, res: any) => {
    let response: any = {};

    try {
        // Gets User Object
        const user = req.user;

        const productItemId = req.body.productItemId;
        const quantity = req.body.quantity;
        let currency = req.body.currency || CURRENCIES.INR;

        if (!Object.values(CURRENCIES).includes(Number(currency))) {
            response = {
                status: false,
                message: `Invalid Currency`,
            };
            return handleResponse(res, response, 400);
        }

        if (!productItemId || typeof Number(quantity) != "number") {
            response = {
                status: false,
                message: `Invalid Params`,
            };
            return handleResponse(res, response, 400);
        }

        const pdtItem = await query(getProductItemByIdQuery, [productItemId]);

        if (!pdtItem.rowCount) {
            response = {
                status: false,
                message: `Invalid product item id`,
            };
            return handleResponse(res, response, 400);
        }

        if (
            !pdtItem.rows[0].is_active ||
            (pdtItem.rows[0].is_limited_quantity &&
                pdtItem.rows[0].available_quantity == 0)
        ) {
            response = {
                status: false,
                message: `Product not available`,
            };
            return handleResponse(res, response, 400);
        }

        // This is where we check for TL cart
        let isProcessOrderTrue = false;

        const cartItems = await getCartItemsByArtistId(user.id, null, currency);

        if (!cartItems || cartItems.length === 0) {
            isProcessOrderTrue = true;
        } else {
            const isTL = cartItems[0].artist_id == 8;
            const incomingArtistId = pdtItem.rows[0].artist_id;
        
            if (isTL && incomingArtistId == 8) {
                isProcessOrderTrue = true;
            } else if (incomingArtistId == 8) {
                isProcessOrderTrue = false;
                response = {
                    status: false,
                    message: `Can't add Terence Lewis' items because user has other artist's items in cart`,
                };
                return handleResponse(res, response, 400);
            } else if (!isTL && incomingArtistId != 8) {
                isProcessOrderTrue = true;
            } else {
                isProcessOrderTrue = false;
            }
        }

        if (isProcessOrderTrue) {
        
            await query(transactionBegin, []);
        
            const userCart = await upsertUserCart(user.id, pdtItem.rows[0].artist_id);
        
            if (!userCart) {
                await query(transactionRollback, []);
                response = {
                    status: false,
                    message: `User Cart not available`,
                };
                return handleResponse(res, response, 400);
            }

            await updateCartItem(userCart.id, productItemId, quantity, currency);
        
            await query(transactionCommit, []);
        
            response = {
                status: true,
                message: `Cart Updated`,
            };
            return handleResponse(res, response, 200);
        } else {
            response = {
                status: false,
                message: `Can't add other items because user has Terence Lewis' items in cart`,
            };
            return handleResponse(res, response, 400);
        }
        } catch (error) {
        await query(transactionRollback, []);
        response = {
            status: false,
            message: `Request failed`,
            error: error,
        };
        return handleResponse(res, response, 500);
    }
};

export const bulkUpdateCart = async (req: any, res: any) => {
    let response: any = {};
    const user = req.user;
    const products: OrderBody[] = req.body.products;
    
    if (!products.length) {
        response = {
            status: false,
            message: `Invalid Params`,
        };
        return handleResponse(res, response, 400);
    }

    try {
        // Validate currency for all products
        for (let i = 0; i < products.length; i++) {
            let currency = products[i].currency || CURRENCIES.INR;
            
            if (!Object.values(CURRENCIES).includes(Number(currency))) {
                response = {
                    status: false,
                    message: `Invalid Currency`,
                };
                return handleResponse(res, response, 400);
            }
            
            if (!products[i].productItemId || typeof Number(products[i].quantity) != "number") {
                response = {
                    status: false,
                    message: `Invalid Params`,
                };
                return handleResponse(res, response, 400);
            }
        }

        // Get all product items and validate them
        const productItems = [];
        for (let i = 0; i < products.length; i++) {
            const pdtItem = await query(getProductItemByIdQuery, [
                products[i].productItemId,
            ]);

            if (!pdtItem.rowCount) {
                response = {
                    status: false,
                    message: `Invalid product item id`,
                };
                return handleResponse(res, response, 400);
            }

            if (
                !pdtItem.rows[0].is_active ||
                (pdtItem.rows[0].is_limited_quantity &&
                    pdtItem.rows[0].available_quantity == 0)
            ) {
                response = {
                    status: false,
                    message: `Product not available`,
                };
                return handleResponse(res, response, 400);
            }

            productItems.push(pdtItem.rows[0]);
        }

        // Check for TL cart logic
        let isProcessOrderTrue = false;
        const cartItems = await getCartItemsByArtistId(user.id, null, products[0].currency || CURRENCIES.INR);
        
        if (!cartItems || cartItems.length === 0) {
            isProcessOrderTrue = true;
        } else {
            const isTL = cartItems[0].artist_id == 8;
            
            // Check if all incoming products are from the same artist type (TL vs non-TL)
            const incomingArtistIds = productItems.map(item => item.artist_id);
            const hasTerrenceLewisItems = incomingArtistIds.some(id => id == 8);
            const hasNonTerrenceLewisItems = incomingArtistIds.some(id => id != 8);
            
            // If trying to add both TL and non-TL items in the same bulk update
            if (hasTerrenceLewisItems && hasNonTerrenceLewisItems) {
                response = {
                    status: false,
                    message: `Can't mix Terence Lewis' items with other artists' items in the same update`,
                };
                return handleResponse(res, response, 400);
            }
            
            if (isTL && hasTerrenceLewisItems) {
                isProcessOrderTrue = true;
            } else if (hasTerrenceLewisItems) {
                isProcessOrderTrue = false;
                response = {
                    status: false,
                    message: `Can't add Terence Lewis' items because user has other artist's items in cart`,
                };
                return handleResponse(res, response, 400);
            } else if (!isTL && hasNonTerrenceLewisItems) {
                isProcessOrderTrue = true;
            } else {
                isProcessOrderTrue = false;
            }
        }

        if (isProcessOrderTrue) {
            await query(transactionBegin, []);

            await query(deleteOrderQuery, [user.id]);

            for (let i = 0; i < products.length; i++) {
                const userCart = await upsertUserCart(user.id, productItems[i].artist_id);

                if (!userCart) {
                    await query(transactionRollback, []);
                    response = {
                        status: false,
                        message: `User Cart not available`,
                    };
                    return handleResponse(res, response, 400);
                }
                
                await updateCartItem(
                    userCart.id,
                    products[i].productItemId,
                    products[i].quantity,
                    products[i].currency || CURRENCIES.INR
                );
            }

            await query(transactionCommit, []);

            response = {
                status: true,
                message: `Cart Updated`,
            };
            return handleResponse(res, response, 200);
        } else {
            response = {
                status: false,
                message: `Can't add other items because user has Terence Lewis' items in cart`,
            };
            return handleResponse(res, response, 400);
        }
    } catch (error) {
        await query(transactionRollback, []);
        response = {
            status: false,
            message: `Request failed`,
            error: error,
        };
        return handleResponse(res, response, 500);
    }
};

export const getUserCart = async (req: any, res: any) => {
    let response: any = {};

    let { artistId = null } = req.params;

    if (artistId == "undefined") {
        artistId = null;
    }

    const { coupon } = req.query;

    let currency = req.query.currency || CURRENCIES.INR;

    if (!Object.values(CURRENCIES).includes(Number(currency))) {
        response = {
            status: false,
            message: `Invalid Currency`,
        };
        return handleResponse(res, response, 400);
    }

    try {
        const user = req.user;
        let discountData: any;
        if (coupon) {
            const couponData = await getOfferData(coupon);
        
            if (couponData.length) {
                discountData = couponData[0];
            }
        }

        if (discountData) {
            await validateOfferData(discountData, user.id);
        }

        const cartItems = await getCartItemsByArtistId(user.id, artistId, currency);

        // This is where we check how many courses of TL does the user has in the cart right now
        // let amount, baseAmount;
        const terenceCoursescount = cartItems.filter(
            (item) => item.artist_id === '8' && item.product_type === 2
        ).length;
        
        let terenceItems: any[] = [];
        if (terenceCoursescount >= 2 && !coupon){
            if (terenceCoursescount === 2 || terenceCoursescount === 9) {
                // 2 courses and full suite
                const discountPercentage = await query(getDiscountPercentageQuery, [terenceCoursescount === 2 ? 10 : 9]);
                cartItems.forEach(item => {
                    if (item.product_type === 2) {
                        
                        const reducedPrice = reducePercent(item.price, item.tax, discountPercentage.rows[0].discount);
                        const reducedPerQty = reducePercent(item.price_per_qty, item.tax, discountPercentage.rows[0].discount);
                        
                        terenceItems.push({
                            ...item,
                            price: reducedPrice,
                            price_per_qty: reducedPerQty,
                            original_price: Number(item.price),
                            offer_applied: true
                        });
                    } else {
                    terenceItems.push(item);
                  }
                });
                
            } else if (terenceCoursescount >= 3 && terenceCoursescount < 9) {
                // 3 to 8 courses
                const discountPercentage = await query(getDiscountPercentageQuery, [8]);
                let numberOfCoursesDiscounted = 0;
                cartItems.forEach((item, index) => {

                  if (item.product_type === 2 && numberOfCoursesDiscounted < 3) {

                    const reducedPrice = reducePercent(item.price, item.tax, discountPercentage.rows[0].discount);
                    const reducedPerQty = reducePercent(item.price_per_qty, item.tax, discountPercentage.rows[0].discount);

                    terenceItems.push({
                      ...item,
                      price: reducedPrice,
                      price_per_qty: reducedPerQty,
                      original_price: Number(item.price),
                      offer_applied: true
                    });
                    numberOfCoursesDiscounted++;
                  } else {
                    terenceItems.push(item);
                  }
                });
            }
            // amount = terenceItems.reduce((total, { price }) => total + Number(price), 0);
            // baseAmount = terenceItems.reduce((total, { original_price }) => total + Number(original_price), 0);
        }

        let items: any[] = [];
        if(terenceCoursescount <= 1){
            for (let index = 0; index < cartItems.length; index++) {
                const element = cartItems[index];
                
                let dis = 0;
                if (discountData && Number(discountData.discount) > 0) {
                    dis = Number(discountData.discount);
                } else if (
                    element.discount_enabled &&
                    coupon &&
                    element.coupon.code.toUpperCase() == coupon.toUpperCase()
                ) {
                    await validateOfferData(element.coupon, user.id);
                    dis = Number(element.coupon.discount);
                    discountData = element.coupon;
                } else {
                }
            
                const reducedPrice = reducePercent(element.price, element.tax, dis);
                const reducedPerQty = reducePercent(element.price_per_qty, element.tax, dis);
            
                const finalItem = {
                    ...element,
                    price: reducedPrice,
                    price_per_qty: reducedPerQty,
                    originalPrice: Number(element.price),
                    offer_applied: dis ? true : false
                };

                items.push(finalItem);
            }
        }

        response = {
            status: true,
            message: `Cart Fetched`,
            data: {
                cartItems: !items.length ? terenceItems : items,
            },
        };

        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error,
        };
        return handleResponse(res, response, 500);
    }
};

export const addAddress = async (req: any, res: any) => {
    let response: any = {};
    try {
        const user = req.user;

        const address: UserAddress = req.body;

        if (
            !address.first_name ||
            !address.last_name ||
            !address.address_line_one ||
            !address.city ||
            !address.state ||
            !address.pincode ||
            !address.phone ||
            !address.email
        ) {
            response = {
                status: false,
                message: `Missing Params`,
            };
            return handleResponse(res, response, 400);
        }

        const addAddressDb = await query(addAddressQuery, [
            user.id,
            address.first_name,
            address.last_name,
            address.address_line_one,
            address.address_line_two,
            address.city,
            address.state,
            address.pincode,
            address.phone,
            address.email,
            address.country,
            address.address_line_three,
        ]);

        if (!addAddressDb.rowCount) {
            response = {
                status: false,
                message: `Error in saving Address`,
            };
            return handleResponse(res, response, 400);
        }

        response = {
            status: true,
            message: `Address saved`,
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error,
        };
        return handleResponse(res, response, 500);
    }
};

export const getAddressAll = async (req: any, res: any) => {
    let response: any = {};
    try {
        const user = req.user;

        const address = await query(getAddresswithidQuery, [user.id]);

        if (!address.rowCount) {
            response = {
                status: true,
                data:{
                    address:[]
                },
                message: `No Address found`,
            };
            return handleResponse(res, response, 200);
        }

        response = {
            status: true,
            message: `Address found`,
            data: {
                address: address.rows,
            },
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Requst failed`,
        };
        return handleResponse(res, response, 500);
    }
};

export const updateAddress = async (req: any, res: any) => {
    let response: any = {};
    try {
        const user = req.user;

        const address: UserAddress = req.body;

        if (
            !address.first_name ||
            !address.last_name ||
            !address.address_line_one ||
            !address.city ||
            !address.state ||
            !address.pincode ||
            !address.phone ||
            !address.email
        ) {
            response = {
                status: false,
                message: `Missing Params`,
            };
            return handleResponse(res, response, 400);
        }

        const addAddressDb = await query(updateAddressQuery, [
            user.id,
            address.first_name,
            address.last_name,
            address.address_line_one,
            address.address_line_two,
            address.city,
            address.state,
            address.pincode,
            address.phone,
            address.email,
            address.country,
            address.address_line_three,
            address.id,
        ]);

        if (!addAddressDb.rowCount) {
            response = {
                status: false,
                message: `Error in saving Address`,
            };
            return handleResponse(res, response, 400);
        }

        response = {
            status: true,
            message: `Address saved`,
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error,
        };
        return handleResponse(res, response, 500);
    }
};

export const deleteAddress = async (req: any, res: any) => {
    let response: any = {};
    try {
        const address: UserAddress = req.body;

        if (!address.id) {
            response = {
                status: false,
                message: `Missing Params`,
            };
            return handleResponse(res, response, 400);
        }

        try {
            const deleteResult = await query(deleteAddressQuery, [address.id]);
            
            if (!deleteResult.rowCount) {
                response = {
                    status: false,
                    message: `No address found with ID: ${address.id}`,
                };
                return handleResponse(res, response, 404);
            }

            response = {
                status: true,
                message: `Address Deleted`,
            };
            return handleResponse(res, response, 200);

        } catch (error: any) {
            // Foreign key violation
            if (error.code === '23503') {
                console.warn(`Address ID ${address.id} is in use. Marking as not in use.`);

                await query(markAddressNotInUseQuery, [address.id]);

                response = {
                    status: true,
                    message: `Address is in use, marked as not in use instead of deletion.`,
                };
                return handleResponse(res, response, 200);
            }

            // Other DB errors
            throw error;
        }

    } catch (error) {
        console.error('Unhandled error during address delete:', error);
        response = {
            status: false,
            message: `Request failed`,
            error,
        };
        return handleResponse(res, response, 500);
    }
};

export const getUserProfile = async (req: any, res: any) => {
    let response: any = {};
    try {
        const user = req.user;

        const userProfile = await query(getuserProfilequery, [user.id]);

        if (!userProfile.rowCount) {
            response = {
                status: false,
                message: `user not found`,
            };
            return handleResponse(res, response, 200);
        }

        response = {
            status: true,
            message: `user found`,
            data: {
                user: userProfile.rows,
            },
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Requst failed`,
        };
        return handleResponse(res, response, 500);
    }
};

export const updateUserProfile = async (req: any, res: any) => {
    let response: any = {};
    try {
        const user = req.user;
        const { default_address, name, email, phone } = req.body;
        if (!req.body) {
            response = {
                status: false,
                message: `Data Not Available`,
            };
            return handleResponse(res, response, 400);
        }

        const userProfile = await query(updateuserProfilequery, [
            name,
            email,
            phone,
            user.id,
            default_address,
        ]);

        if (!userProfile.rowCount) {
            response = {
                status: false,
                message: `User Profile not found`,
            };
            return handleResponse(res, response, 400);
        }

        response = {
            status: true,
            message: `User Profile updated`,
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Requst failed`,
        };
        return handleResponse(res, response, 500);
    }
};

export const updateUserProfilePic = async (req: any, res: any) => {
    let response: any = {};

    try {
        const user = req.user;

        if (!req.file) {
            response = {
                status: false,
                message: `Data Not Available`,
            };
            return handleResponse(res, response, 400);
        }

        const filePath = req.file.path;
        const fileSize = await getFileSize(filePath);
        const maxSizeInBytes = 200 * 1024 * 1024; // 1 MB in bytes

        if (fileSize > maxSizeInBytes) {
            response = {
                status: false,
                message: `Video file is larger than expected`,
            };
            return handleResponse(res, response, 400);
        }

        const timestamp = Date.now();
        const outputFileName = `user_${user.id}_profileImg_${timestamp}`;

        // Upload the saved path to cloud storage
        const uploadDir = "profile/upload";
        const awsUrl = await uploadFile(outputFileName, filePath, uploadDir);

        const userProfile = await query(updateuserProfileImgquery, [
            awsUrl,
            user.id,
        ]);

        if (!userProfile.rowCount) {
            const userProfile = await query(insertuserProfileImgquery, [
                awsUrl,
                user.id,
            ]);

            if (userProfile.rowCount) {
                response = {
                    status: true,
                    message: `Created User Profile`,
                };
                return handleResponse(res, response, 200);
            } else {
                response = {
                    status: true,
                    message: `Failed To Create User Profile`,
                };
                return handleResponse(res, response, 200);
            }
        }

        response = {
            status: true,
            message: `User Profile updated`,
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        console.error(error);
        response = {
            status: false,
            message: `Requst failed`,
        };
        return handleResponse(res, response, 500);
    }
};

export const insertUserProfile = async (req: any, res: any) => {
    let response: any = {};
    try {
        const user = req.user;
        const { name, email, phone, image, defaultaddress } = req.body;

        const userProfile = await query(insertuserProfilequery, [
            name,
            email,
            image,
            phone,
            user.id,
            defaultaddress,
        ]);

        if (!userProfile.rowCount) {
            response = {
                status: false,
                message: `User Profile not found`,
            };
            return handleResponse(res, response, 400);
        }

        response = {
            status: true,
            message: `User Profile updated`,
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Requst failed`,
        };
        return handleResponse(res, response, 500);
    }
};

export const getAddress = async (req: any, res: any) => {
    let response: any = {};
    try {
        const user = req.user;

        const addressIfDfltAvail = await query(getAddressQueryDfltAddNotNull, [user.id]);
        const userProfile = await query(getuserProfilequery, [user.id]);

        if (!userProfile.rowCount || !userProfile.rows[0].default_address) {
            const fallbackAddress = await query(getAddressQuery, [user.id]);

            if (!fallbackAddress.rowCount) {
                response = {
                    status: true,
                    data:{
                    address:[],
                },
                    message: `Address not found`,
                };
                return handleResponse(res, response, 200);
            }

            response = {
                status: true,
                message: `Address found`,
                data: {
                    address: fallbackAddress.rows[0],
                },
            };
            return handleResponse(res, response, 200);
        }

        const defaultAddressId = Number(userProfile.rows[0].default_address);

        const defaultAddress = addressIfDfltAvail.rows.find(
            (addr: any) => addr.address_id === defaultAddressId
        );

        if (!defaultAddress) {
            response = {
                    status: true,
                    data:{
                    address:[],
                },
                    message: `Address not found`,
                };
                return handleResponse(res, response, 200);
        }

        response = {
            status: true,
            message: `Address found`,
            data: {
                address: defaultAddress,
            },
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
        };
        return handleResponse(res, response, 500);
    }
};

export const placeOrder = async (req: any, res: any) => {
    let response: any = {};
    try {
        const user = req.user;

        const artistId = req.body.artistId;
        const address: UserAddress = req.body.address;
        const address_id: number = req.body.address_id;
        const coupon = req.body.coupon;

        let discountData: any;
        if (coupon) {
            const couponData = await getOfferData(coupon);
            if (couponData.length) {
                discountData = couponData[0]
            }
        }

        if (discountData) {
            await validateOfferData(discountData, user.id);
        }

        //update address
        let finalAddressId: number = address_id;

        if (!address_id && address) {
            if (
                !address.first_name ||
                !address.last_name ||
                !address.address_line_one ||
                !address.city ||
                !address.state ||
                !address.pincode ||
                !address.phone ||
                !address.email
            ) {
                response = {
                    status: false,
                    message: `Missing Params`,
                };
                return handleResponse(res, response, 400);
            }

            const addAddressDb = await query(addAddressQuery, [
                user.id,
                address.first_name,
                address.last_name,
                address.address_line_one,
                address.address_line_two,
                address.city,
                address.state,
                address.pincode,
                address.phone,
                address.email,
                address.country,
                address.address_line_three,
            ]);

            if (!addAddressDb.rowCount) {
                response = {
                    status: false,
                    message: `Error in saving Address`,
                };
                return handleResponse(res, response, 400);
            }

            finalAddressId = addAddressDb.rows[0].id;
        }

        await query(transactionBegin, []);

        const cartItems = await getCartItemsByArtistId(
            user.id,
            artistId ? artistId : null,
            CURRENCIES.INR
        );

        if (!cartItems.length) {
            await query(transactionRollback, []);
            response = {
                status: false,
                message: `Cart is Empty`,
            };
            return handleResponse(res, response, 400);
        }

        let items: any[] = [];
        for (let index = 0; index < cartItems.length; index++) {
            const element = cartItems[index];
            let dis = 0;
            if (discountData && Number(discountData.discount) > 0) {
                dis = Number(discountData!.discount);
            } else if (element.discount_enabled && coupon && element.coupon.code.toUpperCase() == coupon.toUpperCase()) {
                await validateOfferData(element.coupon, user.id);
                dis = Number(element.coupon.discount);
                discountData = element.coupon
            }

            items.push({
                ...element,
                price: reducePercent(element.price, element.tax, dis),
                price_per_qty: reducePercent(element.price_per_qty, element.tax, dis),
                original_price: Number(element.price),
                offer_applied: dis ? true : false
            })
        }

        const amount = items.reduce(
            (total, { price }) => total + Number(price),
            0
        );

        const baseAmount = items.reduce(
            (total, { original_price }) => total + Number(original_price),
            0
        );

        const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
        const data: PayInitBody = {
            merchantTransactionId: txnId,
            amount: parseInt((Number(amount) * 100).toString()),
            merchantUserId: user.id
        }

        const callbackUrl = process.env.CALLBACK_URL;

        const payment = await runPayAPI(data, callbackUrl!!);

        if (!payment.status) {
            await query(transactionRollback, []);
        }

        const order = await createOrderDb(
            items,
            user.id,
            data.merchantTransactionId,
            finalAddressId,
            amount,
            CURRENCIES.INR,
            PAYMENT_METHOD.PHONEPE,
            baseAmount,
            discountData ? discountData.id : null
        );

        if (!order.status) {
            await query(transactionRollback, []);
            response = {
                status: false,
                message: `Error on placing order`,
            };
            return handleResponse(res, response, 400);
        }

        await query(transactionCommit, []);
        response = {
            status: true,
            message: `Order Created`,
            data: {
                selectedPayment: payment
            },
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Requst failed`,
        };
        return handleResponse(res, response, 500);
    }
};

export const orderNow = async (req: any, res: any) => {
    let response: any = {};
    const user = req.user;

    const { quantity, productItemId } = req.body;
    let address: UserAddress = req.body.address;
    const address_id: number = req.body.address_id;
    let currency = req.body.currency || CURRENCIES.INR;
    const payment_method: number = req.body.payment_method || PAYMENT_METHOD.PAYPAL;

    if (!Object.values(CURRENCIES).includes(Number(currency))) {
        response = {
            status: false,
            message: `Invalid Currency`,
        };
        return handleResponse(res, response, 400);
    }

    const coupon = req.body.coupon;

    if (!quantity || !productItemId) {
        response = {
            status: false,
            message: `Missing Params`,
        };
        return handleResponse(res, response, 400);
    }

    //update address

    let addressData: any = {
        address_id
    };
    if (address_id) {
        let result = await query(getUserAddressByIdQuery, [address_id]);
        if (result.rowCount) {
            address = result.rows[0];
            addressData = {
                address_id: result.rows[0].id,
                ...result.rows[0]
            };
        }
    }

    if (
        !address.first_name ||
        !address.last_name ||
        !address.address_line_one ||
        !address.city ||
        !address.state ||
        !address.pincode ||
        !address.phone ||
        !address.email
    ) {
        response = {
            status: false,
            message: `Missing Params`,
        };
        return handleResponse(res, response, 400);
    }
    try {

        let discountData: any;
        if (coupon) {
            const productResult = await query(getProductItemByIdQuery, [productItemId]);
            if (productResult.rowCount) {
                const couponData = await getOfferData(coupon, undefined, undefined, undefined, productResult.rows[0].product_id);
                if (couponData.length) {
                    discountData = couponData[0]
                }
            }
        }

        if (discountData) {
            const countData = await query(offerUsedCountQuery, [user.id, discountData.id]);

            if (!discountData.unlimited) {
                if (countData.rowCount) {
                    if (countData.rows[0].total_count >= discountData.total_limit) {
                        response = {
                            status: false,
                            message: `Invalid Coupon`
                        }
                        return handleResponse(res, response, 400);
                    }
                }
            }

            if (discountData.user_limited) {
                if (countData.rows[0].user_count >= discountData.user_limit) {
                    response = {
                        status: false,
                        message: `Invalid Coupon`
                    }
                    return handleResponse(res, response, 400);
                }
            }
        }

        // await query(transactionBegin, []);
        if (!address_id) {
            const addAddressDb = await query(addAddressQuery, [user.id, address.first_name, address.last_name, address.address_line_one, address.address_line_two, address.city, address.state, address.pincode, address.phone, address.email, address.country, address.address_line_three]);

            if (!addAddressDb.rowCount) {
                response = {
                    status: false,
                    message: `Error in saving Address`,
                };
                return handleResponse(res, response, 400);
            }

            addressData = {
                address_id: addAddressDb.rows[0].id,
                ...addAddressDb.rows[0]
            };
        }


        const cartItems = await query(productItemQuery, [
            productItemId,
            quantity,
            currency,
        ]);

        if (!cartItems.rowCount) {
            // await query(transactionRollback, []);
            response = {
                status: false,
                message: `Product not found`,
            };
            return handleResponse(res, response, 400);
        }

        let amount = cartItems.rows.reduce(
            (total, { price }) => total + Number(price),
            0
        );

        const baseAmount = amount;

        let tax = cartItems.rows.reduce(
            (total, { tax }) => total + Number(tax),
            0
        );

        //Discounting price
        if (discountData && Number(discountData.discount) > 0) {
            amount = reducePercent(amount, tax, Number(discountData.discount));
        }

        let payment: any;
        if (payment_method == PAYMENT_METHOD.PAYPAL && currency == CURRENCIES.USD) {
            amount += PAYPAL_PLATFORM_FEES * amount;
            payment = await createOrder(Number(amount).toFixed(2).toString(), 'USD');
        } else if (payment_method == PAYMENT_METHOD.PAYGLOCAL && currency == CURRENCIES.USD) {
            const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
            const data: PayGLocalTxnData = {
                totalAmount: Number(amount).toFixed(2).toString(),
                txnCurrency: "USD",
                billingData: {
                    firstName: addressData.first_name || addressData.firstName || '',
                    lastName: addressData.last_name || addressData.lastName || '',
                    addressStreet1: addressData.address_line_one,
                    addressCity: addressData.city,
                    addressState: addressData.state,
                    addressPostalCode: addressData.pincode,
                    addressCountry: "US",
                    emailId: addressData.email
                }
            }
            payment = await initPayment(txnId, data);
        } else {
            // phonePe
            const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
            const data: PayInitBody = {
                merchantTransactionId: txnId,
                amount: parseInt((Number(amount) * 100).toString()),
                merchantUserId: user.id
            }
            const callBackUrl = process.env.PHONEPE_CALLBACK_URL
            payment = await runPayAPI(data, callBackUrl!);
        }

        if (!cartItems.rows.length) {
            await query(transactionRollback, []);
            response = {
                status: false,
                message: `Cart is Empty`,
            };
            return handleResponse(res, response, 400);
        }

        let items: any[] = [];
        for (let index = 0; index < cartItems.rows.length; index++) {
            const element = cartItems.rows[index];
            let dis = 0;
            if (discountData && Number(discountData.discount) > 0) {
                dis = Number(discountData!.discount);
            } else if (element.discount_enabled && coupon && element.coupon.code.toUpperCase() == coupon.toUpperCase()) {
                await validateOfferData(element.coupon, user.id);
                dis = Number(element.coupon.discount);
            }

            items.push({
                ...element,
                price: reducePercent(element.price, element.tax, dis),
                price_per_qty: reducePercent(element.price_per_qty, element.tax, dis),
                original_price: Number(element.price),
                offer_applied: dis ? true : false
            })
        }

        const order = await createOrderDb(
            items,
            user.id,
            payment.order_id,
            addressData.address_id,
            amount,
            currency,
            payment.payment_method,
            baseAmount,
            discountData ? discountData.id : null
        );

        if (!order.status) {
            // await query(transactionRollback, []);
            response = {
                status: false,
                message: `Error on placing order`,
            };
            return handleResponse(res, response, 400);
        }

        // await query(transactionCommit, []);
        response = {
            status: true,
            message: `Order Created`,
            data: {
                selectedPayment: {
                    ...payment,
                    id: order.data
                }
            }
        }
        return handleResponse(res, response, 200);
    } catch (error: any) {
        // await query(transactionRollback, []);
        response = {
            status: false,
            message: error.message ? error.message : `Requst failed`
        }
        return handleResponse(res, response, 500);
    }
};

export const placeOrderV2 = async (req: any, res: any) => {
    let response: any = {};
    try {
        const user = req.user;
        const artistId = req.body.artistId;
        const address: UserAddress = req.body.address;
        const address_id: number = req.body.address_id;
        const payment_method: number = req.body.payment_method || PAYMENT_METHOD.PAYPAL;

        const coupon = req.body.coupon;

        // let discountData: any;
        // if (coupon) {
        //     const couponData = await getOfferData(coupon);
        //     if (couponData.length) {
        //         discountData = couponData[0]
        //     }
        // }

        // if (discountData) {
        //     await validateOfferData(discountData, user.id);
        // }

        let currency = req.body.currency || CURRENCIES.INR;

        if (!Object.values(CURRENCIES).includes(Number(currency))) {
            response = {
                status: false,
                message: `Invalid Currency`,
            };
            return handleResponse(res, response, 400);
        }


        //update address
        let addressData: any = {
            address_id
        };
        if (!address_id && address) {
            if (
                !address.first_name ||
                !address.last_name ||
                !address.address_line_one ||
                !address.city ||
                !address.state ||
                !address.pincode ||
                !address.phone ||
                !address.email ||
                !payment_method
            ) {
                response = {
                    status: false,
                    message: `Missing Params`,
                };
                return handleResponse(res, response, 400);
            }

            const addAddressDb = await query(addAddressQuery, [
                user.id,
                address.first_name,
                address.last_name,
                address.address_line_one,
                address.address_line_two,
                address.city,
                address.state,
                address.pincode,
                address.phone,
                address.email,
                address.country,
                address.address_line_three,
            ]);

            if (!addAddressDb.rowCount) {
                response = {
                    status: false,
                    message: `Error in saving Address`,
                };
                return handleResponse(res, response, 400);
            }

            addressData = {
                address_id: addAddressDb.rows[0].id,
                ...addAddressDb.rows[0]
            };
        } else {
            const address = await query(getUserAddressByIdQuery, [address_id]);
            addressData = {
                address_id: address.rows[0].id,
                ...address.rows[0]
            };
        }

        await query(transactionBegin, []);

        const cartItems = await getCartItemsByArtistId(
            user.id,
            artistId ? artistId : null,
            currency
        );

        if (!cartItems.length) {
            await query(transactionRollback, []);
            response = {
                status: false,
                message: `Cart is Empty`,
            };
            return handleResponse(res, response, 400);
        }

        
        // This is where we check how many courses of TL does the user has in the cart right now
        let amount, baseAmount;
        const terenceCoursescount = cartItems.filter(
            (item) => item.artist_id === '8' && item.product_type === 2
        ).length;
        
        let terenceItems: any[] = [];
        if (terenceCoursescount >= 2 && !coupon){
            if (terenceCoursescount === 2 || terenceCoursescount === 9) {
                // 2 courses and full suite
                const discountPercentage = await query(getDiscountPercentageQuery, [terenceCoursescount === 2 ? 10 : 9]);
                cartItems.forEach(item => {
                    if (item.product_type === 2) {
                        
                        const reducedPrice = reducePercent(item.price, item.tax, discountPercentage.rows[0].discount);
                        const reducedPerQty = reducePercent(item.price_per_qty, item.tax, discountPercentage.rows[0].discount);
                        
                        terenceItems.push({
                            ...item,
                            price: reducedPrice,
                            price_per_qty: reducedPerQty,
                            original_price: Number(item.price),
                            offer_applied: true
                        });
                    } else {
                    terenceItems.push(item);
                  }
                });
                
            } else if (terenceCoursescount >= 3 && terenceCoursescount < 9) {
                // 3 to 8 courses
                const discountPercentage = await query(getDiscountPercentageQuery, [8]);
                let numberOfCoursesDiscounted = 0;
                cartItems.forEach((item, index) => {

                  if (item.product_type === 2 && numberOfCoursesDiscounted < 3) {

                    const reducedPrice = reducePercent(item.price, item.tax, discountPercentage.rows[0].discount);
                    const reducedPerQty = reducePercent(item.price_per_qty, item.tax, discountPercentage.rows[0].discount);

                    terenceItems.push({
                      ...item,
                      price: reducedPrice,
                      price_per_qty: reducedPerQty,
                      original_price: Number(item.price),
                      offer_applied: true
                    });
                    numberOfCoursesDiscounted++;
                  } else {
                    terenceItems.push(item);
                  }
                });
            }
            amount = terenceItems.reduce((total, { price }) => total + Number(price), 0);
            baseAmount = terenceItems.reduce((total, { original_price }) => total + Number(original_price), 0);
        }
        
        let discountData: any;
        if (coupon) {
            const couponData = await getOfferData(coupon);
            
            if (couponData.length) {
                discountData = couponData[0];
            }
        }

        if (discountData) {
            await validateOfferData(discountData, user.id);
        }
        
        
        let items: any[] = [];
        if(terenceCoursescount <= 1){
            for (let index = 0; index < cartItems.length; index++) {
                const element = cartItems[index];

                let dis = 0;
                if (discountData && Number(discountData.discount) > 0) {
                    dis = Number(discountData.discount);
                } else if (
                    element.discount_enabled &&
                    coupon &&
                    element.coupon.code.toUpperCase() === coupon.toUpperCase()
                ) {
                    await validateOfferData(element.coupon, user.id);
                    dis = Number(element.coupon.discount);
                    discountData = element.coupon;
                }

                const reducedPrice = reducePercent(element.price, element.tax, dis);
                const reducedPerQty = reducePercent(element.price_per_qty, element.tax, dis);

                items.push({
                    ...element,
                    price: reducedPrice,
                    price_per_qty: reducedPerQty,
                    original_price: Number(element.price),
                    offer_applied: dis ? true : false
                });
            
            }
            
            amount = items.reduce((total, { price }) => total + Number(price), 0);
            baseAmount = items.reduce((total, { original_price }) => total + Number(original_price), 0);            
        }

        let payment: any;
        if (payment_method == PAYMENT_METHOD.PAYPAL && currency == CURRENCIES.USD) {
            amount += PAYPAL_PLATFORM_FEES * amount;
            payment = await createOrder(Number(amount).toFixed(2).toString(), 'USD');
        } else if (payment_method == PAYMENT_METHOD.PAYGLOCAL && currency == CURRENCIES.USD) {
            const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
            const data: PayGLocalTxnData = {
                totalAmount: Number(amount).toFixed(2).toString(),
                txnCurrency: "USD",
                billingData: {
                    firstName: addressData.first_name || addressData.firstName || '',
                    lastName: addressData.last_name || addressData.lastName || '',
                    addressStreet1: addressData.address_line_one,
                    addressCity: addressData.city,
                    addressState: addressData.state,
                    addressPostalCode: addressData.pincode,
                    addressCountry: "US",
                    emailId: addressData.email
                }
            }
            payment = await initPayment(txnId, data);
        } else {
            // PhonePe
            const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
            const data: PayInitBody = {
                merchantTransactionId: txnId,
                amount: parseInt((Number(amount) * 100).toString()),
                merchantUserId: user.id
            }
            const callBackUrl = process.env.PHONEPE_CALLBACK_URL
            payment = await runPayAPI(data, callBackUrl!);
        }

        if (!payment) {
            await query(transactionRollback, []);
        }

        const order = await createOrderDb(
            items.length ? items : terenceItems,
            user.id,
            payment.order_id,
            addressData.address_id,
            amount,
            currency,
            payment.payment_method,
            baseAmount,
            discountData ? discountData.id : null
        );

        if (!order.status) {
            await query(transactionRollback, []);
            response = {
                status: false,
                message: `Error on placing order`,
            };
            return handleResponse(res, response, 400);
        }

        await query(transactionCommit, []);
        response = {
            status: true,
            message: `Order Created`,
            data: {
                selectedPayment: {
                    ...payment,
                    id: order.data
                }
            },
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        console.error("Error on placing order v2 : ", JSON.stringify(error));
        response = {
            status: false,
            message: `Requst failed`,
        };
        return handleResponse(res, response, 500);
    }
};

// This callback is for phonePe
export const paymentCallback = async (req: any, res: any) => {
    let response = req.body.response;
    response = decodeBase64(response) as PaymentCallback;
    await query(insertPgLogsQuery, [response.success, response.code, response]);

    // const paymentStatus = await paymentStatusCheck(response.data.merchantTransactionId);

    try {
        if (response.code == PHONE_PE_STATUS.PAYMENT_SUCCESS.name) {
            // Dynamic Input based on customer order
            let orderDetail = await query(orderInvoiceQuery, [
                response.data.merchantTransactionId,
            ]);
            // const invoice_id = await query(getTotalSuccessfulTransactionsForInvoiceNumber, []);
            // orderDetail.rows[0] = { id: Number(invoice_id.rows[0].id) + 1, ...orderDetail.rows[0] };

            if (!orderDetail.rowCount) {
                logger.error("Transaction Id not found");
                return;
            }

            // Dynamic Input based on customer order
            const customerDetail = await query(userAddressinvoiceQuery, [
                response.data.merchantTransactionId,
            ]);

            const invoice = await generatePDF(
                orderDetail.rows[0],
                customerDetail.rows[0],
                "Placed"
            );

            await query(transactionBegin, []);
            await updatePaymentStatus(
                response.data.merchantTransactionId,
                PHONE_PE_STATUS.PAYMENT_SUCCESS.id,
                invoice,
                ORDER_STATUS.PLACED
            );
            await updateOrderStatus(response.data.merchantTransactionId);
            await query(deleteCartItemsByArtistIdQuery, [
                response.data.merchantTransactionId,
            ]);
            await query(transactionCommit, []);
        } else if (
            response.code == PHONE_PE_STATUS.TIMED_OUT.name ||
            response.code == PHONE_PE_STATUS.PAYMENT_ERROR.name ||
            response.code == PHONE_PE_STATUS.PAYMENT_DECLINED.name
        ) {
            let orderDetail = await query(orderInvoiceQuery, [
                response.data.merchantTransactionId,
            ]);

            if (!orderDetail.rowCount) {
                logger.error("Transaction Id not found");
                return;
            }

            await updatePaymentStatus(
                response.data.merchantTransactionId,
                PHONE_PE_STATUS.PAYMENT_ERROR.id,
                "",
                ORDER_STATUS.PAY_FAILED
            );
            await updatePaymentFailed(response.data.merchantTransactionId);
        }
    } catch (error) {
        await query(transactionRollback, []);
        console.error(error);
    }

    return res.send("SUCCESS");
};

export const paymentCallbackV2 = async (req: any, res: any, next: any) => {
    let response = {
        success: false,
        code: 400,
    };
    const data: PaymentCallbackDefault = req.payment_data;

    try {
        if (data.isCompleted) {
            response.success = true;
            response.code = 200;
        }

        await query(insertPgLogsQuery, [response.success, response.code, data]);

        const razorpay_order_id = data.orderId;

        let orderDetail = await query(orderInvoiceQuery, [razorpay_order_id]);
        // const invoice_id = await query(getTotalSuccessfulTransactionsForInvoiceNumber, []);
        // orderDetail.rows[0] = { id: Number(invoice_id.rows[0].id) + 1, ...orderDetail.rows[0] };

        if (!orderDetail.rowCount) {
            return next();
        }

        if (response.success) {
            const customerDetail = await query(userAddressinvoiceQuery, [
                razorpay_order_id,
            ]);
            const invoice = await generatePDF(
                orderDetail.rows[0],
                customerDetail.rows[0],
                "Placed"
            );
            
            await query(transactionBegin, []);

            // Check if we have any courses in the order
            const courses = await query(coursesInCartOrderQuery, [orderDetail.rows[0].id]);
            if (courses.rowCount && courses.rowCount > 0) {
              // Process all courses in the cart
              for (const course of courses.rows) {
                try {
                  const userCourse = await query(
                    insertUserCourseQuery, 
                    [course.id, orderDetail.rows[0].user_id, course.validity]
                  );

                  if (!userCourse.rowCount) {
                    logger.error({
                      message: "Error while giving user access to the course",
                      course_id: course.id,
                      user_id: orderDetail.rows[0].user_id,
                    });
                  } else {
                    logger.info({
                      message: "Successfully granted access to course",
                      course_id: course.id,
                      user_id: orderDetail.rows[0].user_id,
                    });
                  }
                } catch (error) {
                  logger.error({
                    message: "Exception while processing course access",
                    course_id: course.id,
                    user_id: orderDetail.rows[0].user_id,
                    error: error,
                  });
                }
              }
            }

            // Check if we have any paidVideos in the order
            // const paidVideos = await query(paidVideosInCartOrderQuery, [orderDetail.rows[0].id]);
            // if (paidVideos.rowCount && paidVideos.rowCount > 0) {
            //   let offer_id = null;
            //   if (orderDetail.rows[0].coupon_code) {
            //     try {
            //       const offerResult = await query(
            //         `SELECT id FROM coupon_codes WHERE code = $1;`, 
            //         [orderDetail.rows[0].coupon_code]
            //       );
            //       offer_id = offerResult.rowCount ? offerResult.rows[0].id : null;
            //     } catch (error) {
            //       logger.error({
            //         message: "Error fetching coupon code for paid video",
            //         coupon_code: orderDetail.rows[0].coupon_code,
            //         error: error,
            //       });
            //     }
            //   }
          
            //   for (const paidVideo of paidVideos.rows) {
            //     try {
            //       console.log("TXN INSERT VALUES", [
            //         Number(orderDetail.rows[0].user_id),
            //         Number(orderDetail.rows[0].total),
            //         razorpay_order_id,
            //         PHONE_PE_STATUS.PAYMENT_SUCCESS.id,
            //         Number(orderDetail.rows[0].currency == "₹" ? CURRENCIES.INR : CURRENCIES.USD),
            //         paymentMethodMap[orderDetail.rows[0].paymentMethod.toLowerCase()],
            //         Number(orderDetail.rows[0].total)
            //       ]);

            //       await query(insertTxnQuery, [
            //         Number(orderDetail.rows[0].user_id),
            //         Number(orderDetail.rows[0].total),
            //         razorpay_order_id,
            //         PHONE_PE_STATUS.PAYMENT_SUCCESS.id,
            //         Number(orderDetail.rows[0].currency == "₹" ? CURRENCIES.INR : CURRENCIES.USD),
            //         paymentMethodMap[orderDetail.rows[0].paymentMethod.toLowerCase()],
            //         Number(orderDetail.rows[0].total)
            //       ]);

            //       const userId = Number(orderDetail.rows[0].user_id);
            //       const videoId = paidVideo.id;
            //       const txnId = razorpay_order_id;
            //       const isCompleted = true;
            //       const validity = Number(paidVideo.validity);
            //       const addressId = Number(orderDetail.rows[0].address_id);
            //       const offerId = offer_id;
                              
            //       console.log("CHECK TYPES", {
            //         userId,
            //         validity,
            //         addressId,
            //         types: {
            //           userId: typeof userId,
            //           validity: typeof validity,
            //           addressId: typeof addressId,
            //         },
            //       });
                  
            //       await query(inserPaidVideoEntryQuery, [
            //         userId,
            //         videoId,
            //         txnId,
            //         isCompleted,
            //         validity,
            //         addressId,
            //         offerId,
            //       ]);
              
            //       logger.info({
            //         message: "Successfully created transaction and paid video entry",
            //         paid_video_id: paidVideo.id,
            //         user_id: orderDetail.rows[0].user_id,
            //         order_id: razorpay_order_id,
            //       });
            //     } catch (error) {
            //       logger.error({
            //         message: "Error while creating transaction and paid video entry",
            //         paid_video_id: paidVideo.id,
            //         user_id: orderDetail.rows[0].user_id,
            //         order_id: razorpay_order_id,
            //         error: error,
            //       });
            //     }
            //   }
            // }
            await updatePaymentStatus(
                razorpay_order_id,
                PHONE_PE_STATUS.PAYMENT_SUCCESS.id,
                invoice,
                ORDER_STATUS.PLACED
            );
            await updateOrderStatus(razorpay_order_id);
            await query(deleteCartItemsByArtistIdQuery, [razorpay_order_id]);
            await query(transactionCommit, []);
        } else if (!response.success) {
            await updatePaymentStatus(
                razorpay_order_id,
                PHONE_PE_STATUS.PAYMENT_ERROR.id,
                JSON.stringify(req.body),
                ORDER_STATUS.PAY_FAILED
            );
            await updatePaymentFailed(razorpay_order_id);
        }
    } catch (error: unknown) {
      await query(transactionRollback, []);
        
      if (error instanceof Error) {
        logger.error({
          message: "Error on processing merch orders",
          error: error.stack || error.message,
        });
      } else {
        logger.error({
          message: "Unknown error on processing merch orders",
          error: error,
        });
      }
    }
    return res.send("SUCCESS");
};

export const orderList = async (req: any, res: any) => {
    let response: any = {};
    const { page = 1, pageSize = DEFAULT_PAGE_SIZE } = req.query;
    const user = req.user;

    try {
        const orders = await query(userOrdersV2Query, [user.id, pageSize, page]);

        let totalItems = 0;
        if (orders.rowCount) {
            const items = await query(getTotalOrderItemsQuery, [user.id]);
            totalItems = items.rows[0].total_items;
        }

        response = {
            status: true,
            message: `Success`,
            data: {
                orders: orders.rows,
                totalPages: totalItems ? Math.ceil(totalItems / pageSize) : 0,
            },
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Requst failed`,
        };
        return handleResponse(res, response, 500);
    }
};

export const newOrderList = async (req: any, res: any) => {
    let response: any = {};
    const { page = 1, pageSize = DEFAULT_PAGE_SIZE } = req.query;
    const user = req.user;

    try {
        const orders = await query(getOrderHistoryQuery, [user.id, pageSize, page]);

        let totalItems = 0;
        // if (orders.rowCount) {
        //     const items = await query(getTotalOrderItemsQuery, [user.id]);
        //     totalItems = items.rows[0].total_items
        // }

        response = {
            status: true,
            message: `Success`,
            data: {
                orders: orders.rows,
                totalPages: totalItems ? Math.ceil(totalItems / pageSize) : 0,
            },
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Requst failed`,
        };
        return handleResponse(res, response, 500);
    }
};

export const orderDetail = async (req: any, res: any) => {
    let response: any = {};
    const { order_item_id, order_type } = req.query;

    const user = req.user;

    if (!order_item_id || !order_type) {
        response = {
            status: false,
            message: `Missing Params`,
        };
        return handleResponse(res, response, 400);
    }

    try {
        const orderDetail = await getOrderDetail(
            order_item_id,
            user.id,
            order_type
        );

        const address = await getPurchaseAddress(
            order_type,
            user.id,
            orderDetail.address_id
        );

        const productsWithSubtotal = await getPurchasedProducts(
            order_item_id,
            order_type
        );

        const total = productsWithSubtotal.reduce(
            (totalSum, { total }) => totalSum + Number(total),
            0
        );
        const subTotal = productsWithSubtotal.reduce(
            (total, { subtotal }) => total + Number(subtotal),
            0
        );

        const discount = await query(getDiscountQuery, [productsWithSubtotal[0]?.offer_id]);
        let is_cancellable = false;
        if (
            orderDetail.is_cancellable == true &&
            orderDetail.order_status === "PLACED" &&
            orderDetail.payment_status === "PAYMENT_SUCCESS"
        ) {
            is_cancellable = true;
        }

        response = {
            status: true,
            message: `Success`,
            data: {
                detail: {
                    ...orderDetail,
                    order_type,
                    total: total.toFixed(2),
                    subTotal: subTotal.toFixed(2),
                    discount: discount.rows[0],
                    tax: Number(total - subTotal).toFixed(2),
                    address: address,
                    is_cancellable,
                },
                products: productsWithSubtotal,
            },
        };
        return handleResponse(res, response, 200);
    } catch (error: any) {
        response = {
            status: false,
            message: error.message ? error.message : `Requst failed`,
        };
        return handleResponse(res, response, 500);
    }
};

export const popup = async (req: any, res: any) => {
    let response: any = {};
    const { id } = req.query;

    if (!id) {
        response = {
            status: false,
            message: `Missing Params`,
        };
        return handleResponse(res, response, 400);
    }

    try {
        const orderDetail = await query(getPopupQuery, [id]);

        if (!orderDetail.rowCount) {
            response = {
                status: false,
                message: `Invalid Id`,
            };
            return handleResponse(res, response, 400);
        }

        response = {
            status: true,
            message: `Success`,
            data: {
                ...orderDetail.rows[0],
                images: JSON.parse(orderDetail.rows[0].images),
                video_stream_url: null,
            },
        };

        if (orderDetail.rows[0].video) {
            let videoPath = orderDetail.rows[0].video;
            let result = videoPath.substring(0, videoPath.lastIndexOf('/') + 1) + 'output.m3u8';

            videoPath= generateSignedUrl(result, 30*60);
            let signed_playlist=await getSignedPlaylistWithSegments(videoPath);
            let signed_video_stream_url=`data:application/vnd.apple.mpegurl;base64,${Buffer.from(signed_playlist).toString('base64')}`;
            response.data.video_stream_url = signed_video_stream_url;
        }

        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Requst failed`,
        };
        return handleResponse(res, response, 500);
    }
};

export const video = async (req: any, res: any) => {
    let response: any = {};
    const { id } = req.query;

    if (!id) {
        response = {
            status: false,
            message: `Missing Params`,
        };
        return handleResponse(res, response, 400);
    }

    try {
        const orderDetail = await query(getVidQuery, [id]);

        if (!orderDetail.rowCount) {
            response = {
                status: false,
                message: `Invalid Id`,
            };
            return handleResponse(res, response, 400);
        }

        let videoPath = orderDetail.rows[0].video;
        let result = videoPath.substring(0, videoPath.lastIndexOf('/') + 1) + 'output.m3u8';
        videoPath= generateSignedUrl(result, 30*60)
        let signed_playlist=await getSignedPlaylistWithSegments(videoPath)
        let signed_video_stream_url=`data:application/vnd.apple.mpegurl;base64,${Buffer.from(signed_playlist).toString('base64')}`

        const video = {
            ...orderDetail.rows[0],
            video_stream_url: signed_video_stream_url
        }
        response = {
            status: true,
            message: `Success`,
            data: {
                ...video,
            },
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Requst failed`,
        };
        return handleResponse(res, response, 500);
    }
};

export const updateOrderStatusadmin = async (req: any, res: any) => {
    let response = {};
    try {
        if (!req.file) {
            response = {
                status: false,
                message: `File Not Available`,
            };
            return handleResponse(res, response, 400);
        }

        const filePath = req.file.path;

        if (!fs.existsSync(filePath)) {
            return res.status(404).send("File not found.");
        }

        let path = req.file.path;
        var workbook = XLSX.readFile(path);
        var sheet_name_list = workbook.SheetNames;
        let jsonData: OrderExcelItems[] = XLSX.utils.sheet_to_json(
            workbook.Sheets[sheet_name_list[0]]
        );

        const statuss = ["Delivered", "Shipped"];
        const data = jsonData!.filter((el) => statuss.includes(el.Status));

        if (!data.length) {
            response = {
                status: false,
                message: `No data to update`,
            };
            return handleResponse(res, response, 400);
        }

        let validOrders = data
            .filter((el_) => Number(el_["Billing Name"]) > 0)
            .map((el) => {
                const status = ORDER_STATUS_CSV.find((_el) => _el.name === el.Status);
                return {
                    statusId: status!.id,
                    id: el["Billing Name"],
                    status: el.Status,
                };
            });

        const orders = await query(getOrdersQuery, [
            validOrders.map((el) => {
                return el.id;
            }),
        ]);

        validOrders = validOrders.filter((el) =>
            orders.rows
                .map((el_) => {
                    return el_.id;
                })
                .includes(el.id)
        );

        if (orders.rowCount) {
            for (let index = 0; index < orders.rows.length; index++) {
                const element = orders.rows[index];
                let orderDetail = await query(orderInvoiceQuery, [element.payment_ref]);
                const customerDetail = await query(userAddressinvoiceQuery, [
                    element.payment_ref,
                ]);
                // const invoice_id = await query(getTotalSuccessfulTransactionsForInvoiceNumber, []);
                // orderDetail.rows[0] = { id: invoice_id.rows[0].id, ...orderDetail.rows[0] };

                let sub = "Purchase Invoice";
                const data: OrderDATA = {
                    customerName:
                        customerDetail.rows[0].firstName +
                        " " +
                        customerDetail.rows[0].lastName,
                    total: orderDetail.rows[0].total,
                    address: {
                        line1: customerDetail.rows[0].address.address_line_one,
                        line2: customerDetail.rows[0].address.address_line_two,
                        line3: customerDetail.rows[0].address.address_line_three,
                        city: customerDetail.rows[0].address.city,
                        state: customerDetail.rows[0].address.state,
                        pincode: customerDetail.rows[0].address.pincode,
                    },
                    products: getProductArr(orderDetail.rows[0].products),
                };

                const orderItem = validOrders.find((el) => el.id === element.id);

                if (orderItem?.status == "Delivered") {
                    await generatePDF(
                        orderDetail.rows[0],
                        customerDetail.rows[0],
                        orderItem?.status
                    );
                } else if (orderItem?.status == "Shipped") {
                    const html = mailShippedText(data);
                    const attachments: any[] = [];
                    sub = "Your Order is on the Way!";
                    await sendEmail(
                        [customerDetail.rows[0].email],
                        sub,
                        html,
                        undefined,
                        attachments
                    );
                }
            }
        }

        await updateOrderStatusDB(validOrders);

        removeFile(filePath);

        response = {
            status: true,
            message: `Success`,
            // data: parsedData
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        console.error(error);
        response = {
            status: false,
            message: `Requst failed`,
            error,
        };
        return handleResponse(res, response, 500);
    }
};

export const cancelPayment = async (req: any, res: any) => {
    let response = {};

    const { mTxnId } = req.body;

    if (!mTxnId) {
        response = {
            status: false,
            message: `Missing Params`,
        };
        return handleResponse(res, response, 400);
    }

    try {
        let orderDetail = await query(orderInvoiceQuery, [mTxnId]);

        if (!orderDetail.rowCount) {
            response = {
                status: false,
                message: `TransactionId Not Found`,
            };
            return handleResponse(res, response, 400);
        }

        await updatePaymentStatus(
            mTxnId,
            PHONE_PE_STATUS.PAYMENT_DECLINED.id,
            "",
            ORDER_STATUS.PAY_FAILED
        );
        await updatePaymentFailed(mTxnId);

        response = {
            status: true,
            message: `Success`,
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Requst failed`,
        };
        return handleResponse(res, response, 500);
    }
};

export const getPaymentGateways = async (req: any, res: any) => {
    let response = {};
    try {
        const currency = req.query.currency;

        const pgs = await query("select * from payment_method where currency_id = $1 or $1 is null", [currency ? currency : null])

        response = {
            status: true,
            message: `Success`,
            data: pgs.rows
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Requst failed`,
        };
        return handleResponse(res, response, 500);
    }
}