<template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular :size="70" :width="7" color="white" indeterminate></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <v-container v-else>
    <template v-if="items.length > 0">
      <h3 class="text-h6 white--text mb-4 text-center">Products</h3>
      <div class="product-list mb-4">
        <v-card v-for="(item, index) in items" :key="index" class="mb-4" style="background-color: transparent">
          <v-row no-gutters>
            <v-col cols="4">
              <v-img :src="item.images[0]" height="150"></v-img>
            </v-col>
            <v-col cols="8" class="pa-4 d-flex flex-column">
              <div class="text-h6">{{ item.name }}</div>
              <v-row v-if="Object.values(item.variants)[0]">
                <v-col v-for="(el, index) in filteredVariants(item.variants)" :key="index">
                  <div class="text-subtitle-1 text-capitalize" v-if="el">
                    {{ Object.keys(el)[0] }} : {{ Object.values(el)[0] }}
                  </div>
                </v-col>
              </v-row>
              <div class="mt-auto d-flex justify-space-between align-center">
                <v-btn small icon @click="updateQuantity('-1', item.id)">
                  <v-icon>mdi-minus</v-icon>
                </v-btn>
                <span>{{ item.cart_quantity }}</span>
                <v-btn small icon @click="updateQuantity('1', item.id)" v-if="item.product_type !== 2">
                  <v-icon>mdi-plus</v-icon>
                </v-btn>
                <span>
                  {{ currencySymbol }}
                  <s v-if="items && items.length > 0 && item.original_price > item.price">{{ formatIndianCurrency(item.original_price * item.cart_quantity == 0 ? item.price_per_qty * item.cart_quantity : item.original_price * item.cart_quantity) }}</s>
                    <span class="ml-2">{{ formatIndianCurrency(selectedCurrency === "INR" ? item.price_per_qty * item.cart_quantity : getSubtotal) }}</span>
                </span>
                  
              </div>
            </v-col>
          </v-row>
        </v-card>
      </div>

      <div class="text-center mb-4">
        <v-btn color="white" class="black--text rounded-xl" @click="updateCart">
          Update Cart
        </v-btn>
      </div>

      <v-divider :thickness="1" class="mb-4 border-opacity-100" dark></v-divider>

      <table dark class="mb-6 full-width-table">
        <tbody>
          <tr>
            <td class="text-left">Subtotal:</td>
            <td class="text-right">
              {{
                currencySymbol + formatIndianCurrency(getSubtotal().toFixed(2))
              }}
            </td>
          </tr>
          <tr v-if="tax > 0">
            <td class="text-left">Tax:</td>
            <td class="text-right">
              {{ currencySymbol + formatIndianCurrency(tax.toFixed(2)) }}
            </td>
          </tr>
          <tr>
            <td class="text-left">Shipping:</td>
            <td class="text-right">FREE SHIPPING</td>
          </tr>
          <tr>
            <td class="text-left text-h6">Total:</td>
            <td class="text-right text-h6">
              {{ currencySymbol + formatIndianCurrency(getTotal()) }}
            </td>
          </tr>
        </tbody>
      </table>

      <div class="text-center">
        <v-btn color="white" class="black--text rounded-xl" @click="proceedCheckout">
          Checkout
        </v-btn>
      </div>
    </template>

    <template v-else>
      <div class="flex flex-col justify-center items-center text-center"
        style="display: flex; flex-direction: column; align-items: center; height: calc(100vh - 110px);">
        <v-icon size="48" color="gray">mdi-cart-outline</v-icon>
        <p class="text-lg mt-2">Your cart is empty</p>
        <p class="text-sm">Start adding your favorite items to place an order.</p>
        <v-btn color="primary" class="mt-4" @click="$router.push('/')">
          Browse Products
        </v-btn>
      </div>
    </template>
  </v-container>
</template>

<script setup>
import { formatIndianCurrency } from "@/helper/common";
import { getCart, updateBulkCartAPI ,updateCartAPI} from "@/services/productService";
import { ref, onBeforeMount,onMounted } from "vue";

import { useAppStore } from "@/stores/app";
import { useRoute, useRouter } from "vue-router";
const route = useRoute();

const appStore = useAppStore();
const router = useRouter();

let isLoading = ref(true);
let items = ref([]);
let currency = ref("INR");
let currencySymbol = ref("₹");
let tax = ref(0);
const selectedCurrency = appStore.currencyCode;

const updateArray = (arr, id, updatedData) => {
  return arr.map((item) =>
    item.id === id ? { ...item, ...updatedData } : item
  );
};
const updateQuantity = (num, id) => {
  const data = items.value.filter((el) => {
    if (el.id == id) {
      return true;
    }
  });
  let quantity = data[0].cart_quantity;
  quantity += +num;
  !isNaN(quantity) && quantity > 0 ? quantity : (quantity = 0);
  const newArray = updateArray(items.value, id, {
    cart_quantity: quantity,
  });
  items.value = newArray;
  getSubtotal();
};

const fetchCart = async () => {
  isLoading.value = true
  try {
    const resp = await getCart(
      undefined,
      selectedCurrency === "INR" ? "currency=1" : "currency=2"
    );
    items.value = resp;
    if (resp.length == 0) {
      return (isLoading.value = false);
    }
    currency.value = resp[0].currency;
    currencySymbol.value = resp[0].currency_symbol;
    isLoading.value = false;
  } catch (error) {
    console.error("Error in product detail API", error);
  }
}

const updateCart = async () => {
  isLoading.value = true
  try {
    const toUpdate = items.value.map((el) => {
      return {
        productItemId: el.id,
        quantity: el.cart_quantity,
        currency: selectedCurrency === "INR" ? 1 : 2,
      };
    });
    const resp = await updateBulkCartAPI({
      products: toUpdate,
    });

    if (resp.status) {
      fetchCart()
    } else {
      isLoading.value = false
      console.log(resp, "error");

    }
  } catch (error) {
    console.error("Error in Update Cart", error);
  }
};

const getSubtotal = () => {
  const individualTotal = items.value.map((el) => {
    // Calculate the base price (price before tax)
    const priceBeforeTax =
      Number(el.price_per_qty) / (1 + Number(el.tax) / 100);
    const totalBfrTax = priceBeforeTax * Number(el.cart_quantity);
    return totalBfrTax;
  });

  // Calculate the subtotal (sum of individual totals before tax)
  const subtotal = individualTotal.reduce((a, b) => a + b, 0);

  // Calculate the total tax amount based on the subtotal
  const totalTax = items.value.reduce((total, el) => {
    const priceBeforeTax =
      Number(el.price_per_qty) / (1 + Number(el.tax) / 100);
    const itemTotal = priceBeforeTax * Number(el.cart_quantity);
    const itemTax = (itemTotal * Number(el.tax)) / 100; // Tax amount for each item
    return total + itemTax; // Sum the individual item taxes
  }, 0);

  tax.value = totalTax; // Store the calculated tax amount

  return subtotal; // Return the subtotal without tax
};

const getTotal = () => {
  const indivualTotal = items.value.map((el) => {
    return Number(el.price_per_qty) * Number(el.cart_quantity);
  });

  const total = indivualTotal.reduce((a, b) => {
    return a + b;
  }, 0);

  return total;
};

const proceedCheckout = () => {
  router.push({
    path: "/checkout",
  });
};

const filteredVariants = (variants) => {
  return variants.filter((variant) => {
    const key = Object.keys(variant)[0];
    const value = variant[key];
    return key && value; // Keep only variants with valid key-value pairs
  });
};

const variantKey = (variant) => Object.keys(variant)[0];
const variantValue = (variant) => variant[variantKey(variant)];


onBeforeMount(async () => {
  const { action, payload, redirect } = route.query;
    if (action === 'updateCart' && payload) {
      isLoading.value = true
      try {
        const parsedPayload = JSON.parse(decodeURIComponent(payload));
        const result = await updateCartAPI(parsedPayload);
        if(result.status){
          isLoading.value = false
          router.push(redirect || '/cart');
        }else{
          isLoading.value = false
        }
      } catch (err) {
        console.error('Post-login updateCart failed:', err);
        router.push('/');
      }
    }
  fetchCart()
});
</script>

<style scoped>
.full-width-table {
  width: 100%;
  border-collapse: collapse;
  color: white;
}

.full-width-table td {
  padding: 8px 0;
}

.full-width-table tr:last-child td {
  padding-top: 16px;
  font-weight: bold;
}

.product-list {
  max-height: 500px;
  overflow-y: auto;
  padding-right: 10px;
}

/* Customize scrollbar */
.product-list::-webkit-scrollbar {
  width: 8px;
}

.product-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.product-list::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.product-list::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>
