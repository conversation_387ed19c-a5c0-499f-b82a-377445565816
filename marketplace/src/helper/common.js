export const formatIndianCurrency = (number) => {
  const num = parseFloat(number);
  if (isNaN(num)) return "0.00";

  const [integerPart, decimalPart = "00"] = num.toFixed(2).split(".");

  let formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  formattedInteger = formattedInteger.replace(
    /^(\d+?)(?=(\d\d)+(\d)(?!\d))(\.\d+)?$/g,
    "$1,"
  );

  return `${formattedInteger}.${decimalPart}`;
};

export const checkIfSafari = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  return (
    userAgent.includes("safari") &&
    !userAgent.includes("chrome") &&
    !userAgent.includes("android")
  );
};

export const getDurationString = (duration) => {
  let string = "";

  if (duration.hours) {
    string += duration.hours + "h ";
  }

  if (duration.minutes) {
    string += duration.minutes + "m ";
  }

  if (duration.seconds) {
    string += duration.seconds + "s ";
  }

  return string;
};

export const formatDate = (dateString) => {
  if (!dateString) return "";
  
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) {
    return "today";
  } else if (diffDays === 2) {
    return "yesterday";
  } else if (diffDays <= 7) {
    return `${diffDays - 1} days ago`;
  } else if (diffDays <= 30) {
    const weeks = Math.floor(diffDays / 7);
    return `${weeks} week${weeks > 1 ? 's' : ''} ago`;
  } else if (diffDays <= 365) {
    const months = Math.floor(diffDays / 30);
    return `${months} month${months > 1 ? 's' : ''} ago`;
  } else {
    const years = Math.floor(diffDays / 365);
    return `${years} year${years > 1 ? 's' : ''} ago`;
  }
};

export const isValidIndianMobileNumber = (number) => {
  const pattern = /^(?:(?:\+?91|0)\s?)?[6789]\d{9}$/;
  return pattern.test(number);
};

export const isValidEmail = (email) => {
  const re =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(String(email).toLowerCase());
};

export const isValidPassword = (password) => {
  const result = password.length >= 8;
  return result;
};

export const isValidUserNamePass = (userName, pass) => {
  if (
    (isValidEmail(userName) || isValidIndianMobileNumber(userName)) &&
    isValidPassword(pass)
  ) {
    return true;
  } else {
    return false;
  }
};

export const LINKS = {
  facebook: "https://www.facebook.com/artisteverse.in",
  twitter: "https://x.com/artisteverse",
  instgaram: "https://www.instagram.com/artisteverse",
  linkedin: "https://www.linkedin.com/company/metastar-media/",

  faq: "https://artisteverse.com/faq.php",
  aboutUs: "https://artisteverse.com/about-us.php",
  terms: "https://artisteverse.com/terms-of-use.php",

  refund: "https://artisteverse.com/refund-policy.php",
  contact: "https://artisteverse.com/contact-us.php",
  privacy: "https://artisteverse.com/privacy-policy.php",
};

export const sendDataToParent = (data) => {
  const targetOrigin = "*";
  window.parent.postMessage(data, targetOrigin);
};

export const getArtistName = (id) => {
  switch (Number(id)) {
    case 1:
      return "Planet Bickram";

    case 2:
      return "Manasi Scott";
    case 3:
      return "Purbayan Chatterjee";
    case 4:
      return "Priyadarsini Govind";
    case 5:
      return "Indian Ocean";
    case 8:
      return "Terence Lewis";
    default:
      return "Metastar";
  }
};

export const getOrderType = (id) => {
  switch (Number(id)) {
    case 1:
      return "PRODUCT";
    case 2:
      return "MEET";
    case 3:
      return "COURSE";
    case 4:
      return "PAID_VIDEO";
    default:
      return "PRODUCT";
  }
};

/**
 * Sorts course levels in the specified order: Beginner, Intermediate, Advance, Open
 * @param {Array} levels - Array of course level objects
 * @returns {Array} - Sorted array of course levels
 */
export const sortCourseLevels = (levels) => {
  if (!Array.isArray(levels)) return levels;
  
  const levelOrder = {
    'Beginner': 1,
    'Intermediate': 2,
    'Advance': 3,
    'Advanced': 3, // Handle both spellings
    'Open': 4
  };
  
  return [...levels].sort((a, b) => {
    const levelA = a.level || '';
    const levelB = b.level || '';
    
    const orderA = levelOrder[levelA] || 999; // Unknown levels go to the end
    const orderB = levelOrder[levelB] || 999;
    
    return orderA - orderB;
  });
};

export const DUMMY = {
  message: "Success",
status: true,
data:[
  {
    "id": 7,
    "title": "Contemporary",
    "subTitle": null,
    "description": `<p><strong><span style=\"font-size:12pt;\">CONTEMPORARY</span></strong></p>\n<p><br></p>\n\n<p><strong><span style=\"font-size:11pt;\">TERENCE LEWIS - <em>'THE GURU OF CONTEMPORARY DANCE IN INDIA'</em></span></strong></p>\n\n<p><span style=\"font-size:11pt;\">Terence Lewis, extensively trained in the Contemporary form, from some of the world's most legendary teachers, currently stands as one of India's most prominent figures in this discipline, synonymous with excellence in the form. Contemporary dance is also one of the dance forms that lies at the very core of the Terence Lewis Contemporary Dance Company. As a celebrity Dance Judge on Indian television, Terence was instrumental in bringing the Contemporary form to the Indian masses at large. With Dance India Dance (Seasons 1, 2 &amp; 3), Lewis managed to transform the Contemporary style, (which otherwise was till then alien to most!) into a household catchword.</span></p>\n\n<p><br></p>\n\n<p><span style=\"font-size:11pt;\">For the past 20 years, Lewis has been teaching over the globe, bringing to the world his signature style - the Indo-Contemporary form, combining inflections and elements of Indian classical and folk with Contemporary techniques.</span></p>\n\n<p><br></p>\n\n<p><strong><span style=\"font-size:12pt;\">CONTEMPORARY - A BRIEF BACKGROUND</span></strong></p>\n<p><br></p>\n\n<p><span style=\"font-size:11pt;\">Contemporary is an expressive and evolving style that draws from modern, ballet, and jazz techniques, but breaks away from rigid rules. It focuses on fluidity, emotion, and storytelling through the body.</span></p>\n\n<p><br></p>\n\n<p><span style=\"font-size:11pt;\">Contemporary draws from the principles of breath, release and progressive movement within the body, drawing from several techniques like the Release technique, Contact Improvisation, Flying Low (to name a few), using a lot of Floorwork as well as large dynamic moves.</span></p>\n\n<p><br></p>\n\n<p><span style=\"font-size:11pt;\">The word contemporary means "in the now," and this style reflects just that — always changing, shaped by current ideas, emotions, experiences and is deeply human. It gives dancers the freedom to explore, feel, and express.</span></p>\n\n<p><br></p>\n\n<p><strong><span style=\"font-size:12pt;\">CLASS STRUCTURE</span></strong></p>\n<p><br></p>\n\n<p><span style=\"font-size:11pt;\">The class consists of 2 videos -</span></p>\n\n<ul>\n  <li style=\"list-style-type:disc;font-size:11pt;\">\n    <p><strong><span style=\"font-size:11pt;\">5 Basic Fundamentals</span></strong><span style=\"font-size:11pt;\"> - these offer a slow and clear stepwise breakdown of 5 selected basic steps, fundamental to the style. These are some of the signature moves associated with the style and are like the very building blocks of the dance form. With certain variations and change of pace, these Fundamentals are used frequently in choreography. These 5 Fundamentals will be used in the choreography that will be taught. (PS - there are more fundamental moves, however we have selected the 5 which we think are some of the most prominent and core to the style).</span></p>\n  </li>\n  <li style=\"list-style-type:disc;font-size:11pt;\">\n    <p><strong><span style=\"font-size:11pt;\">Choreography Video</span></strong><span style=\"font-size:11pt;\"> - Here, we teach Choreography, set to a selected chart buster song, specially edited for the class. The Choreography is broken down in phrases with clear and precise instruction, and with plenty of repetition, to aid learning and the mastery of it. The end of the video will have the Choreography Showcase by the Instructor/s.</span></p>\n  </li>\n</ul>\n\n<p><br></p>\n\n<p><strong><span style=\"font-size:12pt;\">👉🏻 Attention</span></strong><span style=\"font-size:11pt;\"> - For maximum output, we advise you to first access and learn the 5 Basic Fundamentals and then hop on to the Choreography Video.</span></p>\n\n<p><br></p>\n\n<p><strong><span style=\"font-size:11pt;\">Advised Footwear</span></strong><span style=\"font-size:11pt;\"> - Barefoot or Socks</span></p>\n\n<p><br></p>\n\n<p><span style=\"font-size:11pt;\">So feel...breathe... release....roll....flow...fly!</span></p>\n`,
    "level": "Beginner",
    "duration": {
        "hours": 0,
        "minutes": 47,
        "seconds": 32
    },
    "language": "English & Hindi",
    "price": 10002,
    "gst": 18,
    "author": {
        "name": "Terence Lewis",
        "image": "https://d2b4rv4q8lb0q0.cloudfront.net/terence/profile/TL-image.JPG"
    },
    "courseIncludes": [
        "Pre-recorded session videos"
    ],
    "image": "https://d2b4rv4q8lb0q0.cloudfront.net/terence/images/Contem_IMG.png",
    "course_path": "terence/courses/3",
    "currencies": [
        {
            "gst": 0,
            "price": 102,
            "currency": "USD",
            "max_price": 102,
            "currency_id": 2
        },
        {
            "gst": 18,
            "price": 10002,
            "currency": "INR",
            "max_price": 10002,
            "currency_id": 1
        }
    ],
    "countries": [
        {
            "id": 123,
            "country_name": "Global",
            "country_id": 2,
            "item_id": 7
        },
        {
            "id": 124,
            "country_name": "India",
            "country_id": 1,
            "item_id": 7
        }
    ],
    "chapters": [
        {
            "id": 54,
            "name": "Teri Deewani Fundamentals",
            "order": 1,
            "playList": [
                {
                    "id": 109,
                    "name": "Fundamentals",
                    "type": "video",
                    "access": 2,
                    "duration": {
                        "minutes": 22,
                        "seconds": 16
                    },
                    "filePath": "terence/contemporary/teri-deewani/Contemp-5-Basic-fundamental_Final/output.m3u8"
                }
            ]
        },
        {
            "id": 55,
            "name": "Teri Deewani Final",
            "order": 2,
            "playList": [
                {
                    "id": 110,
                    "name": "Final",
                    "type": "video",
                    "access": 2,
                    "duration": {
                        "minutes": 25,
                        "seconds": 16
                    },
                    "filePath": "terence/contemporary/teri-deewani/Contemp-Teri-Deewani/output.m3u8"
                }
            ]
        }
    ],
    "purchased": false,
    "created": "2025-06-24T12:18:23.344Z",
    "updated": "2025-06-24T12:18:23.344Z",
    "filter_keyword": [
        "Dance",
        "Contemporary",
        "Teri Deewani"
    ],
    "currency_symbol": "₹",
    "tax": 18,
    "currency": "INR"
},
{
  "id": 8,
  "title": "Contemporary",
  "subTitle": null,
  "description": `<p><strong><span style=\"font-size:12pt;\">CONTEMPORARY</span></strong></p>\n<p><br></p>\n\n<p><strong><span style=\"font-size:11pt;\">TERENCE LEWIS - <em>'THE GURU OF CONTEMPORARY DANCE IN INDIA'</em></span></strong></p>\n\n<p><span style=\"font-size:11pt;\">Terence Lewis, extensively trained in the Contemporary form, from some of the world's most legendary teachers, currently stands as one of India's most prominent figures in this discipline, synonymous with excellence in the form. Contemporary dance is also one of the dance forms that lies at the very core of the Terence Lewis Contemporary Dance Company. As a celebrity Dance Judge on Indian television, Terence was instrumental in bringing the Contemporary form to the Indian masses at large. With Dance India Dance (Seasons 1, 2 &amp; 3), Lewis managed to transform the Contemporary style, (which otherwise was till then alien to most!) into a household catchword.</span></p>\n\n<p><br></p>\n\n<p><span style=\"font-size:11pt;\">For the past 20 years, Lewis has been teaching over the globe, bringing to the world his signature style - the Indo-Contemporary form, combining inflections and elements of Indian classical and folk with Contemporary techniques.</span></p>\n\n<p><br></p>\n\n<p><strong><span style=\"font-size:12pt;\">CONTEMPORARY - A BRIEF BACKGROUND</span></strong></p>\n<p><br></p>\n\n<p><span style=\"font-size:11pt;\">Contemporary is an expressive and evolving style that draws from modern, ballet, and jazz techniques, but breaks away from rigid rules. It focuses on fluidity, emotion, and storytelling through the body.</span></p>\n\n<p><br></p>\n\n<p><span style=\"font-size:11pt;\">Contemporary draws from the principles of breath, release and progressive movement within the body, drawing from several techniques like the Release technique, Contact Improvisation, Flying Low (to name a few), using a lot of Floorwork as well as large dynamic moves.</span></p>\n\n<p><br></p>\n\n<p><span style=\"font-size:11pt;\">The word contemporary means "in the now," and this style reflects just that — always changing, shaped by current ideas, emotions, experiences and is deeply human. It gives dancers the freedom to explore, feel, and express.</span></p>\n\n<p><br></p>\n\n<p><strong><span style=\"font-size:12pt;\">CLASS STRUCTURE</span></strong></p>\n<p><br></p>\n\n<p><span style=\"font-size:11pt;\">The class consists of 2 videos -</span></p>\n\n<ul>\n  <li style=\"list-style-type:disc;font-size:11pt;\">\n    <p><strong><span style=\"font-size:11pt;\">5 Basic Fundamentals</span></strong><span style=\"font-size:11pt;\"> - these offer a slow and clear stepwise breakdown of 5 selected basic steps, fundamental to the style. These are some of the signature moves associated with the style and are like the very building blocks of the dance form. With certain variations and change of pace, these Fundamentals are used frequently in choreography. These 5 Fundamentals will be used in the choreography that will be taught. (PS - there are more fundamental moves, however we have selected the 5 which we think are some of the most prominent and core to the style).</span></p>\n  </li>\n  <li style=\"list-style-type:disc;font-size:11pt;\">\n    <p><strong><span style=\"font-size:11pt;\">Choreography Video</span></strong><span style=\"font-size:11pt;\"> - Here, we teach Choreography, set to a selected chart buster song, specially edited for the class. The Choreography is broken down in phrases with clear and precise instruction, and with plenty of repetition, to aid learning and the mastery of it. The end of the video will have the Choreography Showcase by the Instructor/s.</span></p>\n  </li>\n</ul>\n\n<p><br></p>\n\n<p><strong><span style=\"font-size:12pt;\">👉🏻 Attention</span></strong><span style=\"font-size:11pt;\"> - For maximum output, we advise you to first access and learn the 5 Basic Fundamentals and then hop on to the Choreography Video.</span></p>\n\n<p><br></p>\n\n<p><strong><span style=\"font-size:11pt;\">Advised Footwear</span></strong><span style=\"font-size:11pt;\"> - Barefoot or Socks</span></p>\n\n<p><br></p>\n\n<p><span style=\"font-size:11pt;\">So feel...breathe... release....roll....flow...fly!</span></p>\n`,
  "level": "Intermediate",
  "duration": {
      "hours": 1,
      "minutes": 18,
      "seconds": 11
  },
  "language": "English",
  "price": 10003,
  "gst": 18,
  "author": {
      "name": "Terence Lewis",
      "image": "https://d2b4rv4q8lb0q0.cloudfront.net/terence/profile/TL-image.JPG"
  },
  "courseIncludes": [
      "Pre-recorded session videos"
  ],
  "image": "https://d2b4rv4q8lb0q0.cloudfront.net/terence/images/Contem_IMG.png",
  "course_path": "terence/courses/4",
  "currencies": [
      {
          "gst": 0,
          "price": 103,
          "currency": "USD",
          "max_price": 103,
          "currency_id": 2
      },
      {
          "gst": 18,
          "price": 10003,
          "currency": "INR",
          "max_price": 10003,
          "currency_id": 1
      }
  ],
  "countries": [
      {
          "id": 125,
          "country_name": "Global",
          "country_id": 2,
          "item_id": 8
      },
      {
          "id": 126,
          "country_name": "India",
          "country_id": 1,
          "item_id": 8
      }
  ],
  "chapters": [
      {
          "id": 58,
          "name": "Doori",
          "order": 1,
          "playList": [
              {
                  "id": 111,
                  "name": "Fundamentals",
                  "type": "video",
                  "access": 2,
                  "duration": {
                      "minutes": 22,
                      "seconds": 16
                  },
                  "filePath": "terence/contemporary-intermediate/doori/Contemp-5-Basic-fundamental_Final/output.m3u8"
              },
              {
                  "id": 112,
                  "name": "Final",
                  "type": "video",
                  "access": 2,
                  "duration": {
                      "minutes": 19,
                      "seconds": 23
                  },
                  "filePath": "terence/contemporary-intermediate/doori/Contemporary-Doori_Final/output.m3u8"
              }
          ]
      },
      {
          "id": 59,
          "name": "Malang",
          "order": 2,
          "playList": [
              {
                  "id": 113,
                  "name": "Fundamentals",
                  "type": "video",
                  "access": 2,
                  "duration": {
                      "minutes": 22,
                      "seconds": 16
                  },
                  "filePath": "terence/contemporary-intermediate/malang/Contemp-5-Basic-fundamental_Final/output.m3u8"
              },
              {
                  "id": 114,
                  "name": "Final",
                  "type": "video",
                  "access": 2,
                  "duration": {
                      "minutes": 14,
                      "seconds": 15
                  },
                  "filePath": "terence/contemporary-intermediate/malang/Contemp_Malang_Final/output.m3u8"
              }
          ]
      }
  ],
  "purchased": false,
  "created": "2025-06-24T12:37:43.061Z",
  "updated": "2025-06-24T12:37:43.061Z",
  "filter_keyword": [
      "Dance",
      "Contemporary",
      "Malang",
      "Doori"
  ],
  "currency_symbol": "₹",
  "tax": 18,
  "currency": "INR"
}
]
}